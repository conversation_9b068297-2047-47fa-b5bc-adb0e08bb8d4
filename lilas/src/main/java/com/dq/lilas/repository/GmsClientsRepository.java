package com.dq.lilas.repository;

import com.dq.lilas.domain.GmsClients;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;
import java.util.Optional;

/**
 * Spring Data JPA repository for the GmsClients entity.
 */
@SuppressWarnings("unused")
@Repository
public interface GmsClientsRepository extends JpaRepository<GmsClients, Long> {
    
    /**
     * Find GmsClients by fiscal ID.
     *
     * @param fiscaleId the fiscal ID to search for.
     * @return the GmsClients if found.
     */
    Optional<GmsClients> findByFiscaleId(String fiscaleId);
}
