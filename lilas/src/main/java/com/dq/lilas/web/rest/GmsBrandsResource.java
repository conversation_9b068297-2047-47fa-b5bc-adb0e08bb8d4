package com.dq.lilas.web.rest;

import com.dq.lilas.repository.GmsBrandsRepository;
import com.dq.lilas.service.GmsBrandsService;
import com.dq.lilas.service.dto.GmsBrandsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.GmsBrands}.
 */
@RestController
@RequestMapping("/api/gms-brands")
public class GmsBrandsResource {

    private static final Logger LOG = LoggerFactory.getLogger(GmsBrandsResource.class);

    private static final String ENTITY_NAME = "gmsBrands";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final GmsBrandsService gmsBrandsService;

    private final GmsBrandsRepository gmsBrandsRepository;

    public GmsBrandsResource(GmsBrandsService gmsBrandsService, GmsBrandsRepository gmsBrandsRepository) {
        this.gmsBrandsService = gmsBrandsService;
        this.gmsBrandsRepository = gmsBrandsRepository;
    }

    /**
     * {@code POST  /gms-brands} : Create a new gmsBrands.
     *
     * @param gmsBrandsDTO the gmsBrandsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new gmsBrandsDTO, or with status {@code 400 (Bad Request)} if the gmsBrands has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<GmsBrandsDTO> createGmsBrands(@RequestBody GmsBrandsDTO gmsBrandsDTO) throws URISyntaxException {
        LOG.debug("REST request to save GmsBrands : {}", gmsBrandsDTO);
        if (gmsBrandsDTO.getId() != null) {
            throw new BadRequestAlertException("A new gmsBrands cannot already have an ID", ENTITY_NAME, "idexists");
        }
        gmsBrandsDTO = gmsBrandsService.save(gmsBrandsDTO);
        return ResponseEntity.created(new URI("/api/gms-brands/" + gmsBrandsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, gmsBrandsDTO.getId().toString()))
            .body(gmsBrandsDTO);
    }

    /**
     * {@code PUT  /gms-brands/:id} : Updates an existing gmsBrands.
     *
     * @param id the id of the gmsBrandsDTO to save.
     * @param gmsBrandsDTO the gmsBrandsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated gmsBrandsDTO,
     * or with status {@code 400 (Bad Request)} if the gmsBrandsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the gmsBrandsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<GmsBrandsDTO> updateGmsBrands(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody GmsBrandsDTO gmsBrandsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update GmsBrands : {}, {}", id, gmsBrandsDTO);
        if (gmsBrandsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, gmsBrandsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!gmsBrandsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        gmsBrandsDTO = gmsBrandsService.update(gmsBrandsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, gmsBrandsDTO.getId().toString()))
            .body(gmsBrandsDTO);
    }

    /**
     * {@code PATCH  /gms-brands/:id} : Partial updates given fields of an existing gmsBrands, field will ignore if it is null
     *
     * @param id the id of the gmsBrandsDTO to save.
     * @param gmsBrandsDTO the gmsBrandsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated gmsBrandsDTO,
     * or with status {@code 400 (Bad Request)} if the gmsBrandsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the gmsBrandsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the gmsBrandsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<GmsBrandsDTO> partialUpdateGmsBrands(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody GmsBrandsDTO gmsBrandsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update GmsBrands partially : {}, {}", id, gmsBrandsDTO);
        if (gmsBrandsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, gmsBrandsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!gmsBrandsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<GmsBrandsDTO> result = gmsBrandsService.partialUpdate(gmsBrandsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, gmsBrandsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /gms-brands} : get all the gmsBrands.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of gmsBrands in body.
     */
    @GetMapping("")
    public ResponseEntity<List<GmsBrandsDTO>> getAllGmsBrands(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of GmsBrands");
        Page<GmsBrandsDTO> page = gmsBrandsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /gms-brands/:id} : get the "id" gmsBrands.
     *
     * @param id the id of the gmsBrandsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the gmsBrandsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<GmsBrandsDTO> getGmsBrands(@PathVariable("id") Long id) {
        LOG.debug("REST request to get GmsBrands : {}", id);
        Optional<GmsBrandsDTO> gmsBrandsDTO = gmsBrandsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(gmsBrandsDTO);
    }

    /**
     * {@code DELETE  /gms-brands/:id} : delete the "id" gmsBrands.
     *
     * @param id the id of the gmsBrandsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteGmsBrands(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete GmsBrands : {}", id);
        gmsBrandsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
    
    /**
     * {@code GET  /gms-brands/all} : get all brands without pagination.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of all brands in body.
     */
    @GetMapping("/all")
    public ResponseEntity<List<GmsBrandsDTO>> getAllBrands() {
        LOG.debug("REST request to get all GmsBrands without pagination");
        List<GmsBrandsDTO> brands = gmsBrandsService.findAllBrands();
        return ResponseEntity.ok().body(brands);
    }
}
