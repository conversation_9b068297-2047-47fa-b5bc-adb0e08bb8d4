package com.dq.lilas.web.rest.camunda;

import com.dq.lilas.service.camunda.PromotionWorkflowService;
import org.camunda.bpm.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST controller for managing promotion workflow operations
 */
@RestController
@RequestMapping("/api/promotion-workflow")
public class PromotionWorkflowResource {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionWorkflowResource.class);
    
    private final PromotionWorkflowService promotionWorkflowService;
    
    public PromotionWorkflowResource(PromotionWorkflowService promotionWorkflowService) {
        this.promotionWorkflowService = promotionWorkflowService;
    }
    
    /**
     * Complete the review task for a promotion request
     * @param demandePromotionId the promotion request ID
     * @param reviewDecision the review decision containing approval status and comments
     * @return ResponseEntity with result
     */
    @PostMapping("/complete-review/{demandePromotionId}")
    @PreAuthorize("hasAuthority('Controle_gestion')")
    public ResponseEntity<String> completeReviewTask(
            @PathVariable Long demandePromotionId,
            @RequestBody ReviewDecisionDTO reviewDecision) {
        
        LOG.debug("REST request to complete review task for promotion request: {}", demandePromotionId);
        
        try {
            // Get current user (reviewer) - you might want to extract this from security context
            String reviewerId = "system"; // TODO: Extract from SecurityContext
            
            promotionWorkflowService.completeReviewTask(
                demandePromotionId, 
                reviewDecision.isApproved(), 
                reviewDecision.getComments(),
                reviewerId
            );
            
            String result = reviewDecision.isApproved() ? "approved" : "rejected";
            LOG.info("Successfully completed review task for promotion request: {} with result: {}", 
                    demandePromotionId, result);
            
            return ResponseEntity.ok("Review task completed successfully with result: " + result);
            
        } catch (Exception e) {
            LOG.error("Error completing review task for promotion request: {}", demandePromotionId, e);
            return ResponseEntity.badRequest()
                .body("Error completing review task: " + e.getMessage());
        }
    }
    
    /**
     * Get the current task for a promotion request
     * @param demandePromotionId the promotion request ID
     * @return ResponseEntity with task information
     */
    @GetMapping("/current-task/{demandePromotionId}")
    public ResponseEntity<TaskInfoDTO> getCurrentTask(@PathVariable Long demandePromotionId) {
        LOG.debug("REST request to get current task for promotion request: {}", demandePromotionId);
        
        try {
            Task currentTask = promotionWorkflowService.getCurrentTask(demandePromotionId);
            
            if (currentTask == null) {
                return ResponseEntity.notFound().build();
            }
            
            TaskInfoDTO taskInfo = new TaskInfoDTO();
            taskInfo.setTaskId(currentTask.getId());
            taskInfo.setTaskName(currentTask.getName());
            taskInfo.setAssignee(currentTask.getAssignee());
            taskInfo.setCreated(currentTask.getCreateTime());
            
            return ResponseEntity.ok(taskInfo);
            
        } catch (Exception e) {
            LOG.error("Error getting current task for promotion request: {}", demandePromotionId, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Check if a promotion request has an active workflow process
     * @param demandePromotionId the promotion request ID
     * @return ResponseEntity with process status
     */
    @GetMapping("/process-status/{demandePromotionId}")
    public ResponseEntity<Map<String, Object>> getProcessStatus(@PathVariable Long demandePromotionId) {
        LOG.debug("REST request to get process status for promotion request: {}", demandePromotionId);
        
        try {
            boolean hasActiveProcess = promotionWorkflowService.hasActiveProcess(demandePromotionId);
            
            Map<String, Object> status = Map.of(
                "demandePromotionId", demandePromotionId,
                "hasActiveProcess", hasActiveProcess
            );
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            LOG.error("Error getting process status for promotion request: {}", demandePromotionId, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * DTO for review decision
     */
    public static class ReviewDecisionDTO {
        private boolean approved;
        private String comments;
        
        public boolean isApproved() {
            return approved;
        }
        
        public void setApproved(boolean approved) {
            this.approved = approved;
        }
        
        public String getComments() {
            return comments;
        }
        
        public void setComments(String comments) {
            this.comments = comments;
        }
    }
    
    /**
     * DTO for task information
     */
    public static class TaskInfoDTO {
        private String taskId;
        private String taskName;
        private String assignee;
        private java.util.Date created;
        
        public String getTaskId() {
            return taskId;
        }
        
        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }
        
        public String getTaskName() {
            return taskName;
        }
        
        public void setTaskName(String taskName) {
            this.taskName = taskName;
        }
        
        public String getAssignee() {
            return assignee;
        }
        
        public void setAssignee(String assignee) {
            this.assignee = assignee;
        }
        
        public java.util.Date getCreated() {
            return created;
        }
        
        public void setCreated(java.util.Date created) {
            this.created = created;
        }
    }
}
