package com.dq.lilas.web.rest;

import com.dq.lilas.repository.GmsClientsRepository;
import com.dq.lilas.service.GmsClientsService;
import com.dq.lilas.service.dto.GmsClientsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.GmsClients}.
 */
@RestController
@RequestMapping("/api/gms-clients")
public class GmsClientsResource {

    private static final Logger LOG = LoggerFactory.getLogger(GmsClientsResource.class);

    private static final String ENTITY_NAME = "gmsClients";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final GmsClientsService gmsClientsService;

    private final GmsClientsRepository gmsClientsRepository;

    public GmsClientsResource(GmsClientsService gmsClientsService, GmsClientsRepository gmsClientsRepository) {
        this.gmsClientsService = gmsClientsService;
        this.gmsClientsRepository = gmsClientsRepository;
    }

    /**
     * {@code POST  /gms-clients} : Create a new gmsClients.
     *
     * @param gmsClientsDTO the gmsClientsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new gmsClientsDTO, or with status {@code 400 (Bad Request)} if the gmsClients has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<GmsClientsDTO> createGmsClients(@Valid @RequestBody GmsClientsDTO gmsClientsDTO) throws URISyntaxException {
        LOG.debug("REST request to save GmsClients : {}", gmsClientsDTO);
        if (gmsClientsDTO.getId() != null) {
            throw new BadRequestAlertException("A new gmsClients cannot already have an ID", ENTITY_NAME, "idexists");
        }
        gmsClientsDTO = gmsClientsService.save(gmsClientsDTO);
        return ResponseEntity.created(new URI("/api/gms-clients/" + gmsClientsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, gmsClientsDTO.getId().toString()))
            .body(gmsClientsDTO);
    }

    /**
     * {@code PUT  /gms-clients/:id} : Updates an existing gmsClients.
     *
     * @param id the id of the gmsClientsDTO to save.
     * @param gmsClientsDTO the gmsClientsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated gmsClientsDTO,
     * or with status {@code 400 (Bad Request)} if the gmsClientsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the gmsClientsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<GmsClientsDTO> updateGmsClients(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody GmsClientsDTO gmsClientsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update GmsClients : {}, {}", id, gmsClientsDTO);
        if (gmsClientsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, gmsClientsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!gmsClientsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        gmsClientsDTO = gmsClientsService.update(gmsClientsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, gmsClientsDTO.getId().toString()))
            .body(gmsClientsDTO);
    }

    /**
     * {@code PATCH  /gms-clients/:id} : Partial updates given fields of an existing gmsClients, field will ignore if it is null
     *
     * @param id the id of the gmsClientsDTO to save.
     * @param gmsClientsDTO the gmsClientsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated gmsClientsDTO,
     * or with status {@code 400 (Bad Request)} if the gmsClientsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the gmsClientsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the gmsClientsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<GmsClientsDTO> partialUpdateGmsClients(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody GmsClientsDTO gmsClientsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update GmsClients partially : {}, {}", id, gmsClientsDTO);
        if (gmsClientsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, gmsClientsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!gmsClientsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<GmsClientsDTO> result = gmsClientsService.partialUpdate(gmsClientsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, gmsClientsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /gms-clients} : get all the gmsClients.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of gmsClients in body.
     */
    @GetMapping("")
    public ResponseEntity<List<GmsClientsDTO>> getAllGmsClients(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of GmsClients");
        Page<GmsClientsDTO> page = gmsClientsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /gms-clients/:id} : get the "id" gmsClients.
     *
     * @param id the id of the gmsClientsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the gmsClientsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<GmsClientsDTO> getGmsClients(@PathVariable("id") Long id) {
        LOG.debug("REST request to get GmsClients : {}", id);
        Optional<GmsClientsDTO> gmsClientsDTO = gmsClientsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(gmsClientsDTO);
    }

    /**
     * {@code DELETE  /gms-clients/:id} : delete the "id" gmsClients.
     *
     * @param id the id of the gmsClientsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteGmsClients(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete GmsClients : {}", id);
        gmsClientsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
