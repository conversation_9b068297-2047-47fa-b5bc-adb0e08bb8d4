package com.dq.lilas.web.rest;

import com.dq.lilas.config.GraphClientConfiguration;
import com.dq.lilas.domain.enumeration.AlfrescoSite;
import com.dq.lilas.domain.enumeration.MailType;
import com.dq.lilas.domain.enumeration.OrderStatus;
import com.dq.lilas.service.AttachementService;
import com.dq.lilas.service.GraphMessageService;
import com.dq.lilas.service.MailsService;
import com.dq.lilas.service.OrderService;
import com.dq.lilas.service.dto.AttachementDTO;
import com.dq.lilas.service.dto.BCCheckResponse;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.service.dto.OrderDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.graph.models.*;
import com.microsoft.kiota.serialization.KiotaJsonSerialization;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/notifications")
@RequiredArgsConstructor
public class WebhookController {

    private final GraphMessageService messageService;
    private final GraphClientConfiguration.GraphProperties props;
    private final MailsService mailsService;
    private final AttachementService attachementService;
    private final OrderService orderService;
    private final RestTemplate restTemplate;

    @Value("${ocr.url}")
    String ocrUrl;
    @Value("${alfresco.url}")
    String alfUrl;
    @Value("${alfresco.directory-path}")
    String alfDirectoryPath;
    @Value("${alfresco.default-model-id}")
    String alfModelId;

    /**
     * <p>
     * This method handles the initial
     * <a href="https://docs.microsoft.com/graph/webhooks#notification-endpoint-validation">endpoint
     * validation request sent</a> by Microsoft Graph when the subscription is created.
     *
     * @param validationToken A validation token provided as a query parameter
     * @return a 200 OK response with the validationToken in the text/plain body
     */
    @PostMapping(headers = {"content-type=text/plain"})
    @ResponseBody
    public ResponseEntity<String> handleValidation(
        @RequestParam(value = "validationToken") final String validationToken) {
        return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(validationToken);
    }

    @PostMapping
    public ResponseEntity<String> handleNotifications(@RequestBody @NonNull final String jsonPayload) {
        try {
            ChangeNotificationCollection notifications = KiotaJsonSerialization.deserialize(
                jsonPayload, ChangeNotificationCollection::createFromDiscriminatorValue);

            if (notifications.getValue() == null) {
                return ResponseEntity.accepted().body("");
            }

            for (ChangeNotification change : notifications.getValue()) {
                if (!isValidClientState(change)) {
                    log.warn("Invalid clientState for notification: {}", change.getId());
                    continue;
                }

                Optional<ResourceIdentifiers> identifiers = extractIdentifiers(Objects.requireNonNull(change.getResource()));
                if (identifiers.isEmpty()) {
                    log.warn("Invalid resource format: {}", change.getResource());
                    continue;
                }

                String userId = identifiers.get().userId();
                String messageId = identifiers.get().messageId();

                log.info("Processing notification - User: {}, Message: {}", userId, messageId);
                processMessage(userId, messageId);
            }

            return ResponseEntity.ok().build();

        } catch (IOException e) {
            log.error("Failed to deserialize notification payload: {}", e.getMessage(), e);
        }

        return ResponseEntity.accepted().body("");
    }

    private boolean isValidClientState(ChangeNotification change) {
        return props.getWebhook().getNotificationSecret().equals(change.getClientState());
    }

    private Optional<ResourceIdentifiers> extractIdentifiers(String resource) {
        String[] parts = resource.split("/");
        if (parts.length >= 4) {
            return Optional.of(new ResourceIdentifiers(parts[1], parts[3]));
        }
        return Optional.empty();
    }

    private void processMessage(String userId, String messageId) {
        try {
            Message message = messageService.getMessageById(userId, messageId);
            log.info("Processing message - Subject: {}, Sender: {}",
                message.getSubject(),
                Objects.requireNonNull(Objects.requireNonNull(message.getSender()).getEmailAddress()).getAddress());

            if (!hasValidAttachments(message)) {
                log.info("Message {} has no attachments, skipping processing", messageId);
                return;
            }

            MailsDTO savedMail = saveMail(message);
            processAttachments(userId, messageId, savedMail);

        } catch (Exception e) {
            log.error("Error processing message {} for user {}: {}", messageId, userId, e.getMessage(), e);
        }
    }

    private boolean hasValidAttachments(Message message) {
        return Boolean.TRUE.equals(message.getHasAttachments());
    }

    private MailsDTO saveMail(Message message) {
        MailsDTO mailsDTO = new MailsDTO();
        mailsDTO.setSubject(message.getSubject());
        mailsDTO.setSender(Objects.requireNonNull(Objects.requireNonNull(message.getSender()).getEmailAddress()).getAddress());
        mailsDTO.setMaildate(Instant.now());

        if (message.getBody() != null && message.getBody().getContent() != null) {
            mailsDTO.setMailbody(message.getBody().getContent());
        }

        if (!CollectionUtils.isEmpty(message.getToRecipients())) {
            mailsDTO.setRecipient(Objects.requireNonNull(message.getToRecipients().get(0).getEmailAddress()).getAddress());
        }

        MailsDTO savedMail = mailsService.save(mailsDTO);
        log.info("Saved mail with ID: {}", savedMail.getId());
        return savedMail;
    }

    private void processAttachments(String userId, String messageId, MailsDTO savedMail) {
        try {
            List<Attachment> attachments = messageService.getMessageAttachments(userId, messageId);
            if (CollectionUtils.isEmpty(attachments)) {
                log.warn("Message {} reported having attachments but none were found", messageId);
                updateMailType(savedMail, MailType.NOT_BC);
                return;
            }

            boolean hasBCAttachment = false;
            for (Attachment attachment : attachments) {
                // upload attachment to alfresco
                String docExternalId = uploadAttachment(attachment);

                // save attachment
                AttachementDTO savedAttachment = saveAttachment(attachment, docExternalId);

                hasBCAttachment |= processBCAttachment(attachment, savedMail, savedAttachment);
            }

            if (!hasBCAttachment) {
                updateMailType(savedMail, MailType.NOT_BC);
            }

        } catch (Exception e) {
            log.error("Error processing attachments for message {}: {}", messageId, e.getMessage());
        }
    }

    private AttachementDTO saveAttachment(Attachment attachment, String docExternalId) {
        AttachementDTO attachementDTO = new AttachementDTO();
        attachementDTO.setFilenameattachment(attachment.getName());
        attachementDTO.setSizeAttachement(attachment.getSize() != null ? attachment.getSize().toString() : null);
        attachementDTO.setDatejcattachment(Instant.now());
        attachementDTO.setIdDocAttachment(docExternalId);

        AttachementDTO savedAttachment = attachementService.save(attachementDTO);
        log.info("Saved attachment with ID: {}", savedAttachment.getId());
        return savedAttachment;
    }

    private String uploadAttachment(Attachment attachment) {
        // Define the API endpoint
        String url = String.format("%s?modelId=%s&site=%s&path=%s", alfUrl, alfModelId, AlfrescoSite.gms.name(), alfDirectoryPath);

        // Prepare headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        // Prepare form data
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();

        // Add file
        byte[] fileContent = ((FileAttachment) attachment).getContentBytes();
        formData.add("file", new ByteArrayResource(Objects.requireNonNull(fileContent)) {
            @Override
            public String getFilename() {
                return attachment.getName();
            }
        });

        // Add properties as JSON
        Map<String, Object> properties = new HashMap<>();
        properties.put("category", "test");
        properties.put("sender", "test");
        properties.put("unity", "test");
        properties.put("createdby", "test");
        properties.put("corresptype", "test");
        properties.put("subject", "test");

        formData.add("properties", properties);

        // Create request entity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);

        // Make the POST request
        try {
            log.info("Upload attachment to Alfresco with ID: {}", attachment.getId());
            return restTemplate.postForObject(url, requestEntity, String.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload document: " + e.getMessage(), e);
        }
    }

    private boolean processBCAttachment(Attachment attachment, MailsDTO savedMail, AttachementDTO savedAttachment) {
        byte[] file = ((FileAttachment) attachment).getContentBytes();
        BCCheckResponse bcCheckResponse = checkBCViaExternalAPI(attachment.getName(), file);

        if (bcCheckResponse != null && "OK".equals(bcCheckResponse.getStatus())) {
            createOrder(savedMail);
            updateMailType(savedMail, MailType.BC);

            if (checkRedundancy(savedMail)) {
                updateMailType(savedMail, MailType.REDUNDANT);
            }
            return true;
        }
        return false;
    }

    private void createOrder(MailsDTO savedMail) {
        try {
            // Create JSON payload for the /api/orders/with-details endpoint
            Map<String, Object> orderPayload = createOrderPayload(savedMail);

            // Convert to JSON string
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonPayload = objectMapper.writeValueAsString(orderPayload);

            // Set up headers for the API call
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            // Create request entity
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonPayload, headers);

            // Call the local API endpoint
            String apiUrl = "http://localhost:8080/api/orders/with-details";
            ResponseEntity<OrderDTO> response = restTemplate.postForEntity(apiUrl, requestEntity, OrderDTO.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                OrderDTO createdOrder = response.getBody();
                log.info("Successfully created order with ID: {} via API for BC mail: {}",
                    createdOrder.getId(), savedMail.getId());

                // Update the mail with the created order reference if needed
                updateMailWithOrder(savedMail, createdOrder);
            } else {
                log.warn("API call to create order returned non-success status: {} for mail: {}",
                    response.getStatusCode(), savedMail.getId());
                // Fallback to direct service call
                createOrderFallback(savedMail);
            }

        } catch (Exception e) {
            log.error("Error calling order creation API for mail {}: {}", savedMail.getId(), e.getMessage(), e);
            // Fallback to direct service call
            createOrderFallback(savedMail);
        }
    }

    /**
     * Fallback method to create order directly via service when API call fails
     */
    private void createOrderFallback(MailsDTO savedMail) {
        try {
            OrderDTO orderDTO = new OrderDTO();
            orderDTO.setMails(savedMail);
            orderDTO.setStatus(OrderStatus.WAITING);

            OrderDTO savedOrder = orderService.save(orderDTO);
            log.info("Created order with ID: {} (fallback) for BC mail: {}", savedOrder.getId(), savedMail.getId());
        } catch (Exception e) {
            log.error("Fallback order creation also failed for mail {}: {}", savedMail.getId(), e.getMessage(), e);
        }
    }

    /**
     * Create JSON payload for the order creation API based on mail information
     */
    private Map<String, Object> createOrderPayload(MailsDTO savedMail) {
        Map<String, Object> payload = new HashMap<>();

        // Create header_info section
        Map<String, Object> headerInfo = new HashMap<>();
        headerInfo.put("company", extractCompanyFromMail(savedMail));
        headerInfo.put("N° commande", generateOrderNumber(savedMail));
        headerInfo.put("Date de commande", formatCurrentDate());
        headerInfo.put("sender", savedMail.getSender());
        headerInfo.put("subject", savedMail.getSubject());

        // Add client information if available
        if (savedMail.getRecipient() != null) {
            headerInfo.put("recipient", savedMail.getRecipient());
        }

        payload.put("header_info", headerInfo);

        // Create footer_info section with basic information
        Map<String, Object> footerInfo = new HashMap<>();
        footerInfo.put("Nb de lignes", 1); // Default to 1 line for BC orders
        footerInfo.put("Nb colis", 1); // Default to 1 package
        footerInfo.put("created_from", "webhook_bc_processing");

        payload.put("footer_info", footerInfo);

        // Add mail reference for tracking
        Map<String, Object> mailInfo = new HashMap<>();
        mailInfo.put("id", savedMail.getId());
        mailInfo.put("subject", savedMail.getSubject());
        mailInfo.put("sender", savedMail.getSender());

        payload.put("mail_reference", mailInfo);

        log.debug("Created order payload for mail {}: {}", savedMail.getId(), payload);
        return payload;
    }

    /**
     * Extract company name from mail information
     */
    private String extractCompanyFromMail(MailsDTO savedMail) {
        // Try to extract company from sender domain or subject
        String sender = savedMail.getSender();
        if (sender != null && sender.contains("@")) {
            String domain = sender.substring(sender.indexOf("@") + 1);
            // Map common domains to company names
            if (domain.contains("azur") || domain.contains("cosmetique")) {
                return "AZUR COSMETIQUE";
            } else if (domain.contains("mg") || domain.contains("monoprix")) {
                return "MG";
            } else if (domain.contains("geant")) {
                return "GEANT";
            }
            // Default to domain name
            return domain.toUpperCase();
        }
        return "UNKNOWN_COMPANY";
    }

    /**
     * Generate order number based on mail information
     */
    private String generateOrderNumber(MailsDTO savedMail) {
        // Generate a unique order number based on mail ID and timestamp
        return "BC-" + savedMail.getId() + "-" + System.currentTimeMillis();
    }

    /**
     * Format current date for order creation
     */
    private String formatCurrentDate() {
        return Instant.now().toString();
    }

    /**
     * Update mail with order reference after successful creation
     */
    private void updateMailWithOrder(MailsDTO savedMail, OrderDTO createdOrder) {
        try {
            // This could be used to update mail with order reference if needed
            log.debug("Mail {} successfully linked to order {}", savedMail.getId(), createdOrder.getId());
        } catch (Exception e) {
            log.warn("Could not update mail {} with order reference {}: {}",
                savedMail.getId(), createdOrder.getId(), e.getMessage());
        }
    }

    private void updateMailType(MailsDTO mail, MailType mailType) {
        // Placeholder for mailType update implementation
        log.info("Mail {} should be updated to '{}'", mail.getId(), mailType);
    }

    /**
     * Check if the mail is a Bon de Commande (BC) via external API using RestTemplate
     *
     * @param name      The attachment name
     * @param fileBytes The attachment bytes
     * @return BCCheckResponse
     */
    private BCCheckResponse checkBCViaExternalAPI(String name, byte[] fileBytes) {
        if (fileBytes == null || fileBytes.length == 0) {
            throw new IllegalArgumentException("File bytes cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("File name cannot be null or empty");
        }

        // Create Resource for the file
        Resource fileAsResource = new ByteArrayResource(fileBytes) {
            @Override
            public String getFilename() {
                return name; // Ensure fileName is used in multipart form data
            }
        };

        // Build multipart form data
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file_uploaded", fileAsResource);

        // Set headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        // Create request entity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            // Send the request
            // TODO FIX WHY IS RETURN NO FILE UPLOADED !!!
            ResponseEntity<String> response = restTemplate.postForEntity(ocrUrl, requestEntity, String.class);

            // Parse the JSON response
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(response.getBody(), BCCheckResponse.class);

        } catch (RestClientException e) {
            log.error("Error uploading file to {}", ocrUrl);
            return null;
        } catch (Exception e) {
            log.error("Error parsing JSON response: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Check if the mail is redundant
     *
     * @param mail The saved mail DTO to check
     * @return true if it's redundant, false otherwise
     */
    private boolean checkRedundancy(MailsDTO mail) {
        try {
            // TODO: Inject and call the redundancy check service when it's implemented
            // Example: return redundancyCheckService.isRedundant(mail);

            log.info("Redundancy check service not yet implemented for mail {}", mail.getId());
            return true;

        } catch (Exception e) {
            log.error("Unexpected error during redundancy check for mail {}: {}", mail.getId(), e.getMessage());
            return false;
        }
    }

    private record ResourceIdentifiers(String userId, String messageId) {
    }
}
