package com.dq.lilas.web.rest;

import com.dq.lilas.domain.enumeration.OrderStatus;
import com.dq.lilas.repository.OrderRepository;
import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.service.EmployeeGmsBrandsService;
import com.dq.lilas.service.OrderService;
import com.dq.lilas.service.dto.OrderDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.service.dto.GmsClientsDTO;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.ClientBrandInfoDTO;
import com.dq.lilas.service.dto.BatchOrderInfoDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;

import java.io.File;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Order}.
 */
@RestController
@RequestMapping("/api/orders")
public class OrderResource {

    private static final Logger LOG = LoggerFactory.getLogger(OrderResource.class);

    private static final String ENTITY_NAME = "order";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final OrderService orderService;

    private final OrderRepository orderRepository;
    
    private final OrderDetailsRepository orderDetailsRepository;

    private final EmployeeGmsBrandsService employeeGmsBrandsService;


    public OrderResource(OrderService orderService, OrderRepository orderRepository, OrderDetailsRepository orderDetailsRepository, EmployeeGmsBrandsService employeeGmsBrandsService) {
        this.orderService = orderService;
        this.orderRepository = orderRepository;
        this.orderDetailsRepository = orderDetailsRepository;
        this.employeeGmsBrandsService = employeeGmsBrandsService;
    }

    /**
     * {@code POST  /orders} : Create a new order.
     *
     * @param orderDTO the orderDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new orderDTO, or with status {@code 400 (Bad Request)} if the order has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<OrderDTO> createOrder(@RequestBody OrderDTO orderDTO) throws URISyntaxException {
        LOG.debug("REST request to save Order : {}", orderDTO);
        if (orderDTO.getId() != null) {
            throw new BadRequestAlertException("A new order cannot already have an ID", ENTITY_NAME, "idexists");
        }
        orderDTO = orderService.save(orderDTO);
        return ResponseEntity.created(new URI("/api/orders/" + orderDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, orderDTO.getId().toString()))
            .body(orderDTO);
    }
    
    /**
     * {@code POST  /orders/with-details} : Create a new order with order details.
     *
     * @param jsonData the raw JSON data to create an order with details.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new orderDTO with details, 
     * or with status {@code 400 (Bad Request)} if the order has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/with-details")
    public ResponseEntity<OrderDTO> createOrderWithDetails(@RequestBody JsonNode jsonData) throws URISyntaxException {
        LOG.debug("REST request to save Order with details from raw JSON: {}", jsonData);

        // Create a new OrderDTO with data from the JSON
        OrderDTO orderDTO = new OrderDTO();

        // Handle new template structure with root-level fields
        handleRootLevelFields(jsonData, orderDTO);

        // Handle extracted_data wrapper if present (new template structure)
        JsonNode dataNode = jsonData.has("extracted_data") ? jsonData.get("extracted_data") : jsonData;

        // Store header_info and footer_info as JSON strings
        if (dataNode.has("header_info")) {
            JsonNode headerInfo = dataNode.get("header_info");
            orderDTO.setHearderJson(headerInfo.toString());
            
            // Extract order number if available
            if (headerInfo.has("N° commande")) {
                orderDTO.setOrderNumber(headerInfo.get("N° commande").asText());
            }
            
            // Set order status - default to WAITING
            orderDTO.setStatus(OrderStatus.WAITING);
            
            // Set locked status - default to false
            orderDTO.setLocked(false);
            
            // USE NEW METHODS TO POPULATE REQUIRED FIELDS
            
            // 1. Get company ID from company name
            if (headerInfo.has("company")) {
                String companyName = headerInfo.get("company").asText();
                Optional<Long> companyIdOpt = orderService.getCompanyIdByName(companyName);
                
                if (companyIdOpt.isPresent()) {
                    Long companyId = companyIdOpt.get();
                    
                    // 2. Get next order number and batch info for this company
                    BatchOrderInfoDTO batchOrderInfo = orderService.getNextOrderNumber(companyId);
                    
                    // Set the rank (order number) from the batch info
                    orderDTO.setRank(batchOrderInfo.getNextOrderNumber());
                    
                    // Set the daily batch relationship
                    DailyBatchesDTO dailyBatchesDTO = new DailyBatchesDTO();
                    dailyBatchesDTO.setId(batchOrderInfo.getBatchId());
                    orderDTO.setDailyBatches(dailyBatchesDTO);
                    
                    LOG.info("Set order rank to {} and batch ID to {} for company: {}", 
                            batchOrderInfo.getNextOrderNumber(), batchOrderInfo.getBatchId(), companyName);
                } else {
                    LOG.warn("Company not found with name: {}", companyName);
                    // Set rank to default value
                    orderDTO.setRank(1);
                }
            }
            
            // 3. Get client and brand info from fiscal ID
            if (headerInfo.has("matricule_fiscale")) {
                String fiscaleId = headerInfo.get("matricule_fiscale").asText();
                
                // Set the fiscal ID on the order entity
                orderDTO.setFiscaleId(fiscaleId);
                
                ClientBrandInfoDTO clientBrandInfo = orderService.getClientAndBrandByFiscalId(fiscaleId);
                
                if (clientBrandInfo != null) {
                    // Set the brand ID
                    orderDTO.setBrandId(clientBrandInfo.getBrandId());
                    
                    // Set the GmsClients relationship with fiscal ID
                    GmsClientsDTO gmsClientsDTO = new GmsClientsDTO();
                    gmsClientsDTO.setId(clientBrandInfo.getClientId());
                    gmsClientsDTO.setFiscaleId(fiscaleId); // Include the fiscal ID
                    orderDTO.setGmsClients(gmsClientsDTO);
                    
                    LOG.info("Set brand ID to {} and client ID to {} for fiscal ID: {}", 
                            clientBrandInfo.getBrandId(), clientBrandInfo.getClientId(), fiscaleId);
                } else {
                    LOG.warn("Client/Brand not found with fiscal ID: {}, using default brand ID", fiscaleId);
                    // Set a default brand ID to prevent null pointer exception
                    orderDTO.setBrandId(1L);
                }
            } else {
                LOG.warn("No matricule_fiscale provided in header_info, using default brand ID");
                // Set a default brand ID when fiscal ID is not provided
                orderDTO.setBrandId(1L);
            }
            
            // Extract and map additional header fields to Order entity
            if (headerInfo.has("Date de commande")) {
                // Parse date format "22/05/2024" to LocalDate
                try {
                    String dateStr = headerInfo.get("Date de commande").asText();
                    // Assuming format is dd/MM/yyyy
                    String[] parts = dateStr.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        orderDTO.setOrderDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse order date: {}", headerInfo.get("Date de commande").asText());
                }
            }
            
            if (headerInfo.has("Date de livraison impérative")) {
                // Parse delivery date
                try {
                    String dateStr = headerInfo.get("Date de livraison impérative").asText();
                    String[] parts = dateStr.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        orderDTO.setDeliveryDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse delivery date: {}", headerInfo.get("Date de livraison impérative").asText());
                }
            }
            
            if (headerInfo.has("Rayon")) {
                orderDTO.setRayon(headerInfo.get("Rayon").asText());
            }
            
            if (headerInfo.has("Gr. variantes")) {
                orderDTO.setGrVariantes(headerInfo.get("Gr. variantes").asText());
            }
            
            if (headerInfo.has("Acheteur")) {
                orderDTO.setClientName(headerInfo.get("Acheteur").asText());
            }
            
            // GEANT Template specific header fields
            if (headerInfo.has("No commande")) {
                orderDTO.setOrderNumber(headerInfo.get("No commande").asText());
            }
            
            if (headerInfo.has("Date commande")) {
                // Parse date format "20/05/24 16:27"
                try {
                    String dateStr = headerInfo.get("Date commande").asText();
                    // Extract date part before space (ignore time)
                    String datePart = dateStr.split(" ")[0];
                    String[] parts = datePart.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        // Handle 2-digit year format
                        if (year < 100) {
                            year += 2000; // Assume 20xx for 2-digit years
                        }
                        orderDTO.setOrderDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse GEANT order date: {}", headerInfo.get("Date commande").asText());
                }
            }
            
            if (headerInfo.has("Code fournisseur")) {
                orderDTO.setClientCode(headerInfo.get("Code fournisseur").asText());
            }
            
            if (headerInfo.has("Nom fournisseur")) {
                orderDTO.setClientName(headerInfo.get("Nom fournisseur").asText());
            }
            
            if (headerInfo.has("Contrat")) {
                orderDTO.setContract(headerInfo.get("Contrat").asText());
            }
            
            if (headerInfo.has("Filiere")) {
                orderDTO.setSector(headerInfo.get("Filiere").asText());
            }
            
            // AZUR COSMETIQUE Template specific header fields
            if (headerInfo.has("Date de commande")) {
                // Parse date format "03/02/25 00:01"
                try {
                    String dateStr = headerInfo.get("Date de commande").asText();
                    // Extract date part before space (ignore time)
                    String datePart = dateStr.split(" ")[0];
                    String[] parts = datePart.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        // Handle 2-digit year format
                        if (year < 100) {
                            year += 2000; // Assume 20xx for 2-digit years
                        }
                        orderDTO.setOrderDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse AZUR COSMETIQUE order date: {}", headerInfo.get("Date de commande").asText());
                }
            }
            
            if (headerInfo.has("Contrat commercial")) {
                orderDTO.setContract(headerInfo.get("Contrat commercial").asText());
            }
            
            if (headerInfo.has("Filière")) {
                orderDTO.setSector(headerInfo.get("Filière").asText());
            }
            
            // BC-MEDDIS Template specific header fields (similar to GEANT but different field names)
            if (headerInfo.has("Date commande")) {
                // Parse date format "19/02/25 03:29" 
                try {
                    String dateStr = headerInfo.get("Date commande").asText();
                    // Extract date part before space (ignore time)
                    String datePart = dateStr.split(" ")[0];
                    String[] parts = datePart.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        // Handle 2-digit year format
                        if (year < 100) {
                            year += 2000; // Assume 20xx for 2-digit years
                        }
                        orderDTO.setOrderDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse BC-MEDDIS order date: {}", headerInfo.get("Date commande").asText());
                }
            }
            
            if (headerInfo.has("Nom fournisseur")) {
                orderDTO.setClientName(headerInfo.get("Nom fournisseur").asText());
            }
            
            // UCC Template specific header fields
            if (headerInfo.has("N° commande")) {
                orderDTO.setOrderNumber(headerInfo.get("N° commande").asText());
            }
            
            // UNIVERSAL HEADER FIELDS - Present in all templates
            if (headerInfo.has("Societe")) {
                orderDTO.setCompany(headerInfo.get("Societe").asText());
            }
            
            if (headerInfo.has("Matricule fiscale")) {
                orderDTO.setFiscaleId(headerInfo.get("Matricule fiscale").asText());
            }
            
            if (headerInfo.has("Lieu de livraison")) {
                // Note: This field in header_info represents delivery location from header
                // Different from delivery_info section's "Lieu de livraison"
                String headerDeliveryLocation = headerInfo.get("Lieu de livraison").asText();
                // If delivery location not already set from delivery_info, use header value
                if (orderDTO.getDeliveryLocation() == null || orderDTO.getDeliveryLocation().isEmpty()) {
                    orderDTO.setDeliveryLocation(headerDeliveryLocation);
                }
            }
        }
        
        // Handle delivery_info mapping to Order entity
        if (dataNode.has("delivery_info")) {
            JsonNode deliveryInfo = dataNode.get("delivery_info");
            
            if (deliveryInfo.has("Lieu de livraison")) {
                orderDTO.setDeliveryLocation(deliveryInfo.get("Lieu de livraison").asText());
            }
            
            if (deliveryInfo.has("Destinataire")) {
                orderDTO.setDistinataire(deliveryInfo.get("Destinataire").asText());
            }
            
            // GEANT Template specific delivery fields
            if (deliveryInfo.has("Commande par")) {
                orderDTO.setCommissionar(deliveryInfo.get("Commande par").asText());
            }
            
            if (deliveryInfo.has("Livre a")) {
                orderDTO.setDeliveryLocation(deliveryInfo.get("Livre a").asText());
            }
            
            if (deliveryInfo.has("Commande a")) {
                orderDTO.setClientName(deliveryInfo.get("Commande a").asText());
            }
            
            // AZUR COSMETIQUE Template specific delivery fields
            if (deliveryInfo.has("Commandé par")) {
                orderDTO.setCommissionar(deliveryInfo.get("Commandé par").asText());
            }
            
            if (deliveryInfo.has("Livré à")) {
                orderDTO.setDeliveryLocation(deliveryInfo.get("Livré à").asText());
            }
            
            if (deliveryInfo.has("Commandé à")) {
                orderDTO.setClientName(deliveryInfo.get("Commandé à").asText());
            }
            
            // UCC Template specific delivery fields
            if (deliveryInfo.has("Facturation")) {
                orderDTO.setBillingInfo(deliveryInfo.get("Facturation").asText());
            }
        }
        
        // Handle footer_info mapping to Order entity
        if (dataNode.has("footer_info")) {
            JsonNode footerInfo = dataNode.get("footer_info");
            orderDTO.setFooterJson(footerInfo.toString());
            
            if (footerInfo.has("Nb de lignes")) {
                try {
                    orderDTO.setLineCount(Integer.parseInt(footerInfo.get("Nb de lignes").asText()));
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse line count: {}", footerInfo.get("Nb de lignes").asText());
                }
            }
            
            if (footerInfo.has("Nb colis")) {
                try {
                    orderDTO.setPacksNb(Integer.parseInt(footerInfo.get("Nb colis").asText()));
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse packs number: {}", footerInfo.get("Nb colis").asText());
                }
            }
            
            if (footerInfo.has("Montant achat")) {
                try {
                    String amountStr = footerInfo.get("Montant achat").asText().replace(" DT", "").replace(",", ".");
                    orderDTO.setPurchaseAmount(Double.parseDouble(amountStr));
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse purchase amount: {}", footerInfo.get("Montant achat").asText());
                }
            }
            
            if (footerInfo.has("Volume")) {
                try {
                    String volumeStr = footerInfo.get("Volume").asText().replace(" m", "");
                    if (!volumeStr.trim().isEmpty()) {
                        orderDTO.setVolume(Double.parseDouble(volumeStr));
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse volume: {}", footerInfo.get("Volume").asText());
                }
            }
            
            if (footerInfo.has("Poids brut")) {
                try {
                    String weightStr = footerInfo.get("Poids brut").asText().replace(" kg", "");
                    if (!weightStr.trim().isEmpty()) {
                        orderDTO.setGrossWeight(Double.parseDouble(weightStr));
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse gross weight: {}", footerInfo.get("Poids brut").asText());
                }
            }
            
            // GEANT Template specific footer fields
            if (footerInfo.has("Date de livraison prévue le")) {
                // Parse delivery date format "22/05/24 08:00"
                try {
                    String dateStr = footerInfo.get("Date de livraison prévue le").asText();
                    // Extract date part before space (ignore time)
                    String datePart = dateStr.split(" ")[0];
                    String[] parts = datePart.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        // Handle 2-digit year format
                        if (year < 100) {
                            year += 2000; // Assume 20xx for 2-digit years
                        }
                        orderDTO.setDeliveryDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse GEANT delivery date: {}", footerInfo.get("Date de livraison prévue le").asText());
                }
            }
            
            if (footerInfo.has("Observations:")) {
                orderDTO.setObservation(footerInfo.get("Observations:").asText());
            }
            
            // AZUR COSMETIQUE Template specific footer fields
            if (footerInfo.has("TOTAL HT")) {
                try {
                    String totalHtStr = footerInfo.get("TOTAL HT").asText().trim().replace(" ", "");
                    if (!totalHtStr.isEmpty()) {
                        Double totalHt = Double.parseDouble(totalHtStr);
                        orderDTO.setTotalHt(totalHt);
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse TOTAL HT from AZUR COSMETIQUE template: {}", footerInfo.get("TOTAL HT").asText());
                }
            }
            
            if (footerInfo.has("TVA")) {
                // For now, just log the TVA rate - no specific field in OrderDTO
                LOG.info("TVA rate from AZUR COSMETIQUE template: {}", footerInfo.get("TVA").asText());
            }
            
            if (footerInfo.has("MONTANT TVA TOTAL")) {
                try {
                    String tvaAmountStr = footerInfo.get("MONTANT TVA TOTAL").asText().trim().replace(" ", "");
                    if (!tvaAmountStr.isEmpty()) {
                        Double tvaAmount = Double.parseDouble(tvaAmountStr);
                        orderDTO.setTaxAmount(tvaAmount);
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse MONTANT TVA TOTAL from AZUR COSMETIQUE template: {}", footerInfo.get("MONTANT TVA TOTAL").asText());
                }
            }
            
            if (footerInfo.has("Total TTC")) {
                try {
                    String totalTtcStr = footerInfo.get("Total TTC").asText().trim().replace(" ", "");
                    if (!totalTtcStr.isEmpty()) {
                        Double totalTtc = Double.parseDouble(totalTtcStr);
                        orderDTO.setTotalTtc(totalTtc);
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse Total TTC from AZUR COSMETIQUE template: {}", footerInfo.get("Total TTC").asText());
                }
            }
            
            if (footerInfo.has("Date de livraison")) {
                // Parse delivery date format "04/02/25 23:59"
                try {
                    String dateStr = footerInfo.get("Date de livraison").asText();
                    // Extract date part before space (ignore time)
                    String datePart = dateStr.split(" ")[0];
                    String[] parts = datePart.split("/");
                    if (parts.length == 3) {
                        int day = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]);
                        int year = Integer.parseInt(parts[2]);
                        // Handle 2-digit year format
                        if (year < 100) {
                            year += 2000; // Assume 20xx for 2-digit years
                        }
                        orderDTO.setDeliveryDate(java.time.LocalDate.of(year, month, day));
                    }
                } catch (Exception e) {
                    LOG.warn("Could not parse AZUR COSMETIQUE delivery date: {}", footerInfo.get("Date de livraison").asText());
                }
            }
            
            if (footerInfo.has("Quantite totale")) {
                try {
                    String totalQtyStr = footerInfo.get("Quantite totale").asText().trim().replace(" ", "");
                    if (!totalQtyStr.isEmpty()) {
                        Double totalQty = Double.parseDouble(totalQtyStr);
                        orderDTO.setTotalQuantity(totalQty);
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse Quantite totale from AZUR COSMETIQUE template: {}", footerInfo.get("Quantite totale").asText());
                }
            }
            
            if (footerInfo.has("Assiette")) {
                try {
                    String taxBaseStr = footerInfo.get("Assiette").asText().trim().replace(" ", "");
                    if (!taxBaseStr.isEmpty()) {
                        Double taxBase = Double.parseDouble(taxBaseStr);
                        orderDTO.setTaxBase(taxBase);
                    }
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse Assiette from AZUR COSMETIQUE template: {}", footerInfo.get("Assiette").asText());
                }
            }
        }
        
        if (jsonData.has("footer_info")) {
            orderDTO.setFooterJson(jsonData.get("footer_info").toString());
        }
        
        // Handle related entities if provided
        if (jsonData.has("dailyBatches") && jsonData.get("dailyBatches").has("id")) {
            DailyBatchesDTO dailyBatchesDTO = new DailyBatchesDTO();
            dailyBatchesDTO.setId(jsonData.get("dailyBatches").get("id").asLong());
            orderDTO.setDailyBatches(dailyBatchesDTO);
        }
        
        if (jsonData.has("gmsClients") && jsonData.get("gmsClients").has("id")) {
            GmsClientsDTO gmsClientsDTO = new GmsClientsDTO();
            gmsClientsDTO.setId(jsonData.get("gmsClients").get("id").asLong());
            orderDTO.setGmsClients(gmsClientsDTO);
        }
        
        if (jsonData.has("templateConditions") && jsonData.get("templateConditions").has("id")) {
            TemplateConditionsDTO templateConditionsDTO = new TemplateConditionsDTO();
            templateConditionsDTO.setId(jsonData.get("templateConditions").get("id").asLong());
            orderDTO.setTemplateConditions(templateConditionsDTO);
        }
        
        if (jsonData.has("mails") && jsonData.get("mails").has("id")) {
            MailsDTO mailsDTO = new MailsDTO();
            mailsDTO.setId(jsonData.get("mails").get("id").asLong());
            orderDTO.setMails(mailsDTO);
        }
        
        if (jsonData.has("employee") && jsonData.get("employee").has("id")) {
            EmployeeDTO employeeDTO = new EmployeeDTO();
            employeeDTO.setId(jsonData.get("employee").get("id").asLong());
            orderDTO.setEmployee(employeeDTO);
        }
        
        // Handle rank if provided
        if (jsonData.has("rank")) {
            orderDTO.setRank(jsonData.get("rank").asInt());
        }
        
        // Save the order without details first
        OrderDTO savedOrder = orderService.save(orderDTO);
        
        // Track the total number of items and value for order summary
        int totalItems = 0;
        double totalValue = 0.0;
        
        // Now save the order details separately
        if (dataNode.has("table_info")) {
            JsonNode tableInfo = dataNode.get("table_info");
            // Iterate through pages
            for (Iterator<String> it = tableInfo.fieldNames(); it.hasNext();) {
                String pageName = it.next();
                JsonNode pageRows = tableInfo.get(pageName);
                
                if (pageRows.isArray()) {
                    for (JsonNode row : pageRows) {
                        // Create a minimal OrderDetailsDTO
                        OrderDetailsDTO detailDTO = new OrderDetailsDTO();
                        
                        // Set the parent order reference
                        OrderDTO orderRef = new OrderDTO();
                        orderRef.setId(savedOrder.getId());
                        detailDTO.setOrder(orderRef);
                        
                        // Store the raw JSON for the line
                        detailDTO.setOrderLineJson(row.toString());
                        
                        // Handle different template formats
                        parseOrderDetailFields(row, detailDTO);
                        
                        // Calculate totals if both quantity and price are available
                        if (detailDTO.getQuantity() != null && detailDTO.getUnitPrice() != null) {
                            totalItems += detailDTO.getQuantity().intValue();
                            totalValue += detailDTO.getQuantity().doubleValue() * detailDTO.getUnitPrice();
                        }
                        
                        // Set default values for fields that should have defaults
                        detailDTO.setAvailability(true);
                        // Note: validConditions and injected fields have been removed from the DTO
                        
                        // Set updated fields to match original values if not specified
                        detailDTO.setUpdatedQty(detailDTO.getQuantity());
                        detailDTO.setUpdatedDiscount(detailDTO.getDiscount());
                        detailDTO.setUpdatedUnitPrice(detailDTO.getUnitPrice());
                        detailDTO.setUpdatedBarcode(detailDTO.getBarcode());
                        detailDTO.setUpdatedInternalCode(detailDTO.getInternalCode());
                        
                        // IMPORTANT: Explicitly set the status fields to null to avoid database errors
                        // These fields exist in the entity but not in the database schema
                        detailDTO.setDiscountStatus(null);
                        detailDTO.setPriceStatus(null);
                        detailDTO.setQuantityStatus(null);
                        detailDTO.setProductStatus(null);
                        
                        try {
                            // Save the detail via a simplified service method
                            orderService.saveOrderDetail(detailDTO);
                        } catch (Exception e) {
                            LOG.error("Error saving order detail: {}", e.getMessage(), e);
                            // Continue processing other details even if one fails
                        }
                    }
                }
            }
        }
        
        // Log summary information
        LOG.info("Order {} created with {} items, total value: {}", savedOrder.getId(), totalItems, totalValue);
        
        // INCREMENT THE ORDER NUMBER IN THE BATCH after successful order creation
        if (savedOrder.getDailyBatches() != null && savedOrder.getDailyBatches().getId() != null) {
            try {
                orderService.incrementOrderNumber(savedOrder.getDailyBatches().getId());
                LOG.info("Successfully incremented order number for batch ID: {}", savedOrder.getDailyBatches().getId());
            } catch (Exception e) {
                LOG.error("Failed to increment order number for batch ID: {}, error: {}", 
                         savedOrder.getDailyBatches().getId(), e.getMessage());
                // Don't fail the whole operation, just log the error
            }
        }
        
        // Fetch the updated order with all its details
        Optional<OrderDTO> updatedOrder = orderService.findOne(savedOrder.getId());
        
        return ResponseEntity.created(new URI("/api/orders/" + savedOrder.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, savedOrder.getId().toString()))
            .body(updatedOrder.orElse(savedOrder));
    }

    /**
     * {@code PUT  /orders/:id} : Updates an existing order.
     *
     * @param id the id of the orderDTO to save.
     * @param orderDTO the orderDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated orderDTO,
     * or with status {@code 400 (Bad Request)} if the orderDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the orderDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<OrderDTO> updateOrder(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody OrderDTO orderDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Order : {}, {}", id, orderDTO);
        if (orderDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, orderDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!orderRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        orderDTO = orderService.update(orderDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, orderDTO.getId().toString()))
            .body(orderDTO);
    }

    /**
     * {@code PATCH  /orders/:id} : Partial updates given fields of an existing order, field will ignore if it is null
     *
     * @param id the id of the orderDTO to save.
     * @param orderDTO the orderDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated orderDTO,
     * or with status {@code 400 (Bad Request)} if the orderDTO is not valid,
     * or with status {@code 404 (Not Found)} if the orderDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the orderDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<OrderDTO> partialUpdateOrder(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody OrderDTO orderDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Order partially : {}, {}", id, orderDTO);
        if (orderDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, orderDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!orderRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<OrderDTO> result = orderService.partialUpdate(orderDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, orderDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /orders} : get all the orders.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of orders in body.
     */
    @GetMapping("")
    public ResponseEntity<List<OrderDTO>> getAllOrders(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Orders");
        Page<OrderDTO> page = orderService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /orders/:id} : get the "id" order.
     *
     * @param id the id of the orderDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the orderDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<OrderDTO> getOrder(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Order : {}", id);
        Optional<OrderDTO> orderDTO = orderService.findOne(id);
        return ResponseUtil.wrapOrNotFound(orderDTO);
    }

    /**
     * {@code DELETE  /orders/:id} : delete the "id" order.
     *
     * @param id the id of the orderDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteOrder(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Order : {}", id);
        orderService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code GET  /orders/by-delivery-dates} : get orders by delivery date comparison and status.
     *
     * @param operator the comparison operator (">" or "<=")
     * @param days the number of days to add to current date (typically 1 for systemdate+1)
     * @param status the order status (typically 'V' which should be converted to integer)
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of orders in body.
     */
    @GetMapping("/by-delivery-dates")
    public ResponseEntity<List<OrderDTO>> getOrdersByDeliveryDates(
            @RequestParam String operator,
            @RequestParam(defaultValue = "1") Integer days,
            @RequestParam Integer status) {
        LOG.debug("REST request to get Orders by delivery dates: operator={}, days={}, status={}", operator, days, status);

        // Calculate comparison date as current date + days
        Instant comparisonDate = Instant.now().plus(days, ChronoUnit.DAYS);

        List<OrderDTO> orders = orderService.getOrdersByDeliveryDates(operator, comparisonDate, status);
        return ResponseEntity.ok().body(orders);
    }

    /**
     * {@code GET  /orders/by-status} : get orders by status.
     *
     * @param statusCode the status code ('D' for livré/delivered, 'P' for payé/paid)
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of orders in body.
     */
    @GetMapping("/by-status")
    public ResponseEntity<List<OrderDTO>> getOrdersByStatus(@RequestParam String statusCode) {
        LOG.debug("REST request to get Orders by status: statusCode={}", statusCode);

        List<OrderDTO> orders = orderService.getOrdersByStatus(statusCode);
        return ResponseEntity.ok().body(orders);
    }

    @PostMapping(value = "/test-bon-commande-aziza", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Object> fetchAzizaBonCmmande(@RequestParam("file_uploaded") MultipartFile file) throws Exception {
        LOG.info("Received file: {}", file.getOriginalFilename());

        // Save MultipartFile to a temp file
        LOG.info("Before transfer: MultipartFile size = {} bytes, isEmpty = {}", file.getSize(), file.isEmpty());
        String originalFilename = file.getOriginalFilename();
        String suffix = (originalFilename != null && originalFilename.endsWith(".pdf")) ? ".pdf" : ".tmp";
        File tempFile = File.createTempFile("upload-", suffix);
        try {
            file.transferTo(tempFile);
        } catch (Exception ex) {
            LOG.error("Exception during file.transferTo: {}", ex.getMessage(), ex);
            throw ex;
        }
        LOG.info("After transfer: Temp file exists = {}, size = {} bytes, path = {}", tempFile.exists(), tempFile.length(), tempFile.getAbsolutePath());

        try {
            LOG.info("Temp file created at: {}", tempFile.getAbsolutePath());

            // Prepare file resource for multipart
            FileSystemResource fileResource = new FileSystemResource(tempFile);

            // Build multipart body
            LinkedMultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file_uploaded", fileResource);

            HttpHeaders headers = new HttpHeaders();
            // headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // Log request properties
            LOG.info("Request headers to external API: {}", headers);
            LOG.info("Request body keys to external API: {}", body.keySet());
            LOG.info("Request body values to external API: {}", body.values());
            if (fileResource.exists()) {
                LOG.info("File resource exists: {} (path: {}, size: {} bytes)", fileResource.exists(), fileResource.getPath(), fileResource.contentLength());
            } else {
                LOG.warn("File resource does not exist: {}", fileResource.getPath());
            }

            HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            String apiUrl = "http://*************:8000/api/capture/";
            LOG.info("Sending request to FastAPI: {}", apiUrl);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, requestEntity, String.class);

            LOG.info("FastAPI response status: {}", response.getStatusCode());
            LOG.info("FastAPI response body: {}", response.getBody());

            // Parse JSON response
            String responseBody = response.getBody();
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root;

            try {
                root = mapper.readTree(responseBody);

                // If the root is a text node, try to parse its text as JSON
                if (root.isTextual()) {
                    String innerJson = root.asText();
                    try {
                        root = mapper.readTree(innerJson);
                    } catch (Exception innerEx) {
                        // Still not JSON, return the string
                        return ResponseEntity.ok(innerJson);
                    }
                }

                // Now process as JSON
                ObjectNode headerJson = (ObjectNode) root.path("header_info").deepCopy();
                JsonNode deliveryInfo = root.path("delivery_info");
                if (deliveryInfo.isObject()) {
                    deliveryInfo.fields().forEachRemaining(entry -> headerJson.set(entry.getKey(), entry.getValue()));
                }

                ObjectNode result = mapper.createObjectNode();
                result.set("headerJson", headerJson);
                result.set("footerJson", root.path("footer_info"));
                result.set("orderDetails", root.path("table_info"));

                return ResponseEntity.ok(result);

            } catch (Exception e) {
                // Not JSON at all, return as plain text
                return ResponseEntity.ok(responseBody);
            }

        } finally {
            if (tempFile.exists() && tempFile.delete()) {
                LOG.info("Temp file deleted");
            } else {
                LOG.warn("Failed to delete temp file: {}", tempFile.getAbsolutePath());
            }
        }
    }

    /**
     * Handle root-level fields from new template structure
     */
    private void handleRootLevelFields(JsonNode jsonData, OrderDTO orderDTO) {
        // Handle root-level company field
        if (jsonData.has("Société")) {
            String company = jsonData.get("Société").asText();
            orderDTO.setCompany(company);
            LOG.debug("Set company from root level: {}", company);
        }

        // Handle root-level fiscal ID
        if (jsonData.has("Matricule fiscal") && !jsonData.get("Matricule fiscal").isNull()) {
            String fiscalId = jsonData.get("Matricule fiscal").asText();
            orderDTO.setFiscaleId(fiscalId);
            LOG.debug("Set fiscal ID from root level: {}", fiscalId);

            // Try to get client and brand info from fiscal ID
            try {
                ClientBrandInfoDTO clientBrandInfo = orderService.getClientAndBrandByFiscalId(fiscalId);
                if (clientBrandInfo != null) {
                    orderDTO.setBrandId(clientBrandInfo.getBrandId());

                    GmsClientsDTO gmsClientsDTO = new GmsClientsDTO();
                    gmsClientsDTO.setId(clientBrandInfo.getClientId());
                    gmsClientsDTO.setFiscaleId(fiscalId);
                    orderDTO.setGmsClients(gmsClientsDTO);

                    LOG.info("Set brand ID to {} and client ID to {} from root fiscal ID: {}",
                            clientBrandInfo.getBrandId(), clientBrandInfo.getClientId(), fiscalId);
                } else {
                    LOG.warn("Client/Brand not found with root fiscal ID: {}, using default brand ID", fiscalId);
                    orderDTO.setBrandId(1L);
                }
            } catch (Exception e) {
                LOG.error("Error processing root fiscal ID {}: {}", fiscalId, e.getMessage());
                orderDTO.setBrandId(1L);
            }
        }

        // Handle root-level delivery location
        if (jsonData.has("Lieu de livraison")) {
            String deliveryLocation = jsonData.get("Lieu de livraison").asText();
            orderDTO.setDeliveryLocation(deliveryLocation);
            LOG.debug("Set delivery location from root level: {}", deliveryLocation);
        }
    }

    /**
     * Parse order detail fields from different template formats
     * Supports 8 different template formats: MG, GEANT, UHD, UCC, HYPER, ENTREPOT SFAX, MONOGROS, CARREFOUR, AZIZA
     */
    private void parseOrderDetailFields(JsonNode row, OrderDetailsDTO detailDTO) {
        // Standard template format (c1-c9) - legacy format
        if (row.has("c1")) {
            parseStandardTemplate(row, detailDTO);
        }
        // MG template format (contains "N° article", "EAN principal", "Libellé", etc.)
        else if (row.has("N° article") && row.has("EAN principal") && row.has("Prix de vente unitaire HT")) {
            parseMGTemplate(row, detailDTO);
        }
        // HYPER template format (contains "Prix achat en DIN", "Mt /% Promo", "Colis")
        else if (isHyperTemplate(row)) {
            parseHyperTemplate(row, detailDTO);
        }
        // AZIZA template format (contains "Article", "Quant en UC")
        else if (row.has("Article") && row.has("Quant en UC")) {
            parseAzizaTemplate(row, detailDTO);
        }
        // GEANT template format (to be identified when provided)
        else if (isGeantTemplate(row)) {
            parseGeantTemplate(row, detailDTO);
        }
        // UHD template format (to be identified when provided)
        else if (isUHDTemplate(row)) {
            parseUHDTemplate(row, detailDTO);
        }
        // UCC template format (to be identified when provided)
        else if (isUCCTemplate(row)) {
            parseUCCTemplate(row, detailDTO);
        }
        // ENTREPOT SFAX template format (to be identified when provided)
        else if (isEntrepotSfaxTemplate(row)) {
            parseEntrepotSfaxTemplate(row, detailDTO);
        }
        // MONOGROS template format (to be identified when provided)
        else if (isMonogrosTemplate(row)) {
            parseMonogrosTemplate(row, detailDTO);
        }
        // CARREFOUR template format (to be identified when provided)
        else if (isCarrefourTemplate(row)) {
            parseCarrefourTemplate(row, detailDTO);
        }
        else {
            LOG.warn("Unknown template format for row: {}", row.toString());
        }
    }

    /**
     * Template identification methods - to be implemented when template formats are provided
     */
    private boolean isGeantTemplate(JsonNode row) {
        // GEANT template identification: has "N° article", "EAN principal", "Libellé", "Nb colis", "PCB", "Prix achat en DT"
        // Updated to handle new template format with additional fields
        return row.has("N° article") && row.has("EAN principal") && row.has("Libellé") &&
               row.has("Nb colis") && row.has("PCB") && row.has("Prix achat en DT") &&
               row.has("Qté") && row.has("Montant promo DT") && row.has("TVA");
    }

    private boolean isUHDTemplate(JsonNode row) {
        // UHD template identification: same structure as UCC template
        // Has "Prix achat en DIN" (not DT), "Mt /% Promo" and "Colis" fields
        return row.has("Prix achat en DIN") && row.has("Mt /% Promo") && row.has("Colis") && row.has("N° article");
    }

    private boolean isUCCTemplate(JsonNode row) {
        // UCC template identification: has "Prix achat en DIN" (not DT), "Mt /% Promo" and "Colis" fields
        return row.has("Prix achat en DIN") && row.has("Mt /% Promo") && row.has("Colis") && row.has("N° article");
    }

    private boolean isHyperTemplate(JsonNode row) {
        // HYPER template identification: has "Prix achat en DIN" and "Mt /% Promo" fields
        return row.has("Prix achat en DIN") && row.has("Mt /% Promo") && row.has("Colis");
    }

    private boolean isEntrepotSfaxTemplate(JsonNode row) {
        // TODO: Implement identification logic when ENTREPOT SFAX template is provided
        return false;
    }

    private boolean isMonogrosTemplate(JsonNode row) {
        // MONOGROS template identification: same structure as GEANT template
        // Has "N° article", "EAN principal", "Libellé", "Nb colis", "PCB", "Prix achat en DT"
        return row.has("N° article") && row.has("EAN principal") && row.has("Libellé") && 
               row.has("Nb colis") && row.has("PCB") && row.has("Prix achat en DT");
    }

    private boolean isCarrefourTemplate(JsonNode row) {
        // CARREFOUR MARKET template identification: has "Prix achat en DIN", "Mt /% Promo", "Colis"
        // This is similar to UHD but specifically for CARREFOUR MARKET
        return row.has("N° article") && row.has("EAN principal") && row.has("Libellé") &&
               row.has("Colis") && row.has("PCB") && row.has("Prix achat en DIN") &&
               row.has("Mt /% Promo") && row.has("TVA");
    }

    /**
     * Parse standard template (MG, UHD, UCC, HYPER, ENTREPOT SFAX)
     */
    private void parseStandardTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        // Internal code
        if (row.has("c1")) {
            detailDTO.setInternalCode(row.get("c1").asText());
        }
        
        // Barcode
        if (row.has("c2")) {
            String barcode = row.get("c2").asText().trim();
            if (!barcode.isEmpty()) {
                detailDTO.setBarcode(barcode);
            }
        }
        
        // Quantity
        if (row.has("c4")) {
            try {
                String qtyStr = row.get("c4").asText().trim();
                BigDecimal qty = new BigDecimal(qtyStr);
                detailDTO.setQuantity(qty);
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse quantity: {}", row.get("c4").asText());
            }
        }
        
        // Unit price
        if (row.has("c7")) {
            try {
                String unitPriceStr = row.get("c7").asText().trim().replace(",", ".");
                Double unitPrice = Double.parseDouble(unitPriceStr);
                detailDTO.setUnitPrice(unitPrice);
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse unit price: {}", row.get("c7").asText());
            }
        }
        
        // Discount
        if (row.has("c8")) {
            try {
                String discountStr = row.get("c8").asText().trim();
                if (!discountStr.isEmpty()) {
                    if (discountStr.endsWith("%")) {
                        discountStr = discountStr.substring(0, discountStr.length() - 1);
                    }
                    if (discountStr.endsWith("-")) {
                        discountStr = discountStr.substring(0, discountStr.length() - 1);
                    }
                    // Handle possible decimal format with comma
                    discountStr = discountStr.replace(",", ".");
                    if (discountStr.contains(".")) {
                        // Convert percentage to integer (22.5% becomes 22)
                        Double discountDouble = Double.parseDouble(discountStr);
                        detailDTO.setDiscount(discountDouble.intValue());
                    } else {
                        detailDTO.setDiscount(Integer.parseInt(discountStr));
                    }
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse discount: {}", row.get("c8").asText());
            }
        }
    }

    /**
     * Parse MG template format (new structure)
     */
    private void parseMGTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        // Internal code - from "N° article"
        if (row.has("N° article")) {
            detailDTO.setInternalCode(row.get("N° article").asText());
        }
        
        // Barcode - from "EAN principal"
        if (row.has("EAN principal")) {
            String barcode = row.get("EAN principal").asText().trim();
            if (!barcode.isEmpty()) {
                detailDTO.setBarcode(barcode);
            }
        }
        
        // Product description - from "Libellé"
        if (row.has("Libellé")) {
            String description = row.get("Libellé").asText().trim();
            if (!description.isEmpty()) {
                detailDTO.setProductName(description);
            }
        }
        
        // Brand - from "Marque" (stored in ref field)
        if (row.has("Marque")) {
            String brand = row.get("Marque").asText().trim();
            if (!brand.isEmpty()) {
                detailDTO.setRef(brand);
            }
        }
        
        // Family - from "Famille" (stored in typeUc field)
        if (row.has("Famille")) {
            String family = row.get("Famille").asText().trim();
            if (!family.isEmpty()) {
                detailDTO.setTypeUc(family);
            }
        }
        
        // Packaging - from "Conditionnement" (stored in productUnit field)
        if (row.has("Conditionnement")) {
            String packaging = row.get("Conditionnement").asText().trim();
            if (!packaging.isEmpty()) {
                detailDTO.setProductUnit(packaging);
            }
        }
        
        // Unit price - from "Prix de vente unitaire HT"
        if (row.has("Prix de vente unitaire HT")) {
            try {
                String unitPriceStr = row.get("Prix de vente unitaire HT").asText().trim();
                // Remove currency symbols and normalize decimal separator
                unitPriceStr = unitPriceStr.replace("€", "").replace("DT", "").replace(",", ".").trim();
                if (!unitPriceStr.isEmpty()) {
                    Double unitPrice = Double.parseDouble(unitPriceStr);
                    detailDTO.setUnitPrice(unitPrice);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse unit price: {}", row.get("Prix de vente unitaire HT").asText());
            }
        }
        
        // Quantity - from "Qté commandée"
        if (row.has("Qté commandée")) {
            try {
                String qtyStr = row.get("Qté commandée").asText().trim();
                if (!qtyStr.isEmpty()) {
                    BigDecimal qty = new BigDecimal(qtyStr);
                    detailDTO.setQuantity(qty);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse quantity: {}", row.get("Qté commandée").asText());
            }
        }
        
        // Discount - from "Remise %" if available
        if (row.has("Remise %")) {
            try {
                String discountStr = row.get("Remise %").asText().trim();
                if (!discountStr.isEmpty()) {
                    // Remove % symbol if present
                    if (discountStr.endsWith("%")) {
                        discountStr = discountStr.substring(0, discountStr.length() - 1);
                    }
                    // Handle decimal format with comma
                    discountStr = discountStr.replace(",", ".");
                    if (!discountStr.isEmpty()) {
                        Double discountDouble = Double.parseDouble(discountStr);
                        detailDTO.setDiscount(discountDouble.intValue());
                    }
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse discount percentage: {}", row.get("Remise %").asText());
            }
        }
        // Alternative: If discount is in "Montant remise HT"
        else if (row.has("Montant remise HT")) {
            try {
                String discountAmountStr = row.get("Montant remise HT").asText().trim();
                if (!discountAmountStr.isEmpty()) {
                    // Remove currency symbols and normalize decimal separator
                    discountAmountStr = discountAmountStr.replace("€", "").replace("DT", "").replace(",", ".").trim();
                    if (!discountAmountStr.isEmpty()) {
                        Double discountAmount = Double.parseDouble(discountAmountStr);
                        // Convert absolute discount to percentage if unit price is available
                        if (detailDTO.getUnitPrice() != null && detailDTO.getUnitPrice() > 0) {
                            double discountPercentage = (discountAmount / detailDTO.getUnitPrice()) * 100;
                            detailDTO.setDiscount((int) Math.round(discountPercentage));
                        } else {
                            // Store as flat discount amount (converted to integer)
                            detailDTO.setDiscount(discountAmount.intValue());
                        }
                    }
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse discount amount: {}", row.get("Montant remise HT").asText());
            }
        }
        
        // Total line amount - from "Montant ligne HT" (optional, for validation)
        if (row.has("Montant ligne HT")) {
            try {
                String totalStr = row.get("Montant ligne HT").asText().trim();
                totalStr = totalStr.replace("€", "").replace("DT", "").replace(",", ".").trim();
                if (!totalStr.isEmpty()) {
                    Double totalAmount = Double.parseDouble(totalStr);
                    // Store in a custom field or use for validation
                    // detailDTO.setTotalAmount(totalAmount); // if such field exists
                    LOG.debug("Line total amount: {} for article: {}", totalAmount, detailDTO.getInternalCode());
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse line total: {}", row.get("Montant ligne HT").asText());
            }
        }
    }

    /**
     * Parse AZIZA template format (Enhanced for AZUR COSMETIQUE and similar templates)
     */
    private void parseAzizaTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        LOG.debug("Parsing AZIZA/AZUR COSMETIQUE template format for row: {}", row.toString());
        
        // Product reference - from "Article" field
        if (row.has("Article")) {
            String productRef = row.get("Article").asText().trim();
            if (!productRef.isEmpty()) {
                detailDTO.setRef(productRef);
                detailDTO.setBarcode(productRef); // Also use as barcode
            }
        }
        
        // Product description - from "Libelle article"
        if (row.has("Libelle article")) {
            String productName = row.get("Libelle article").asText().trim();
            if (!productName.isEmpty()) {
                detailDTO.setProductName(productName);
            }
        }
        
        // Quantity - from "Quant en UC"
        if (row.has("Quant en UC")) {
            try {
                String qtyStr = row.get("Quant en UC").asText().trim();
                if (!qtyStr.isEmpty()) {
                    BigDecimal qty = new BigDecimal(qtyStr);
                    detailDTO.setQuantity(qty);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse quantity from AZIZA template: {}", row.get("Quant en UC").asText());
            }
        }
        
        // Unit type - from "Type U.C."
        if (row.has("Type U.C.")) {
            String unitType = row.get("Type U.C.").asText().trim();
            if (!unitType.isEmpty()) {
                detailDTO.setTypeUc(unitType);
            }
        }
        
        // Units per package - from "UVC/UC"
        if (row.has("UVC/UC")) {
            try {
                String uvcStr = row.get("UVC/UC").asText().trim();
                if (!uvcStr.isEmpty()) {
                    Double pcb = Double.parseDouble(uvcStr);
                    detailDTO.setPcb(pcb);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse UVC/UC from AZIZA template: {}", row.get("UVC/UC").asText());
            }
        }
        
        // Product unit - from "Unite"
        if (row.has("Unite")) {
            String productUnit = row.get("Unite").asText().trim();
            if (!productUnit.isEmpty()) {
                detailDTO.setProductUnit(productUnit);
            }
        }
        
        // Set default type if not specified
        if (detailDTO.getTypeUc() == null || detailDTO.getTypeUc().isEmpty()) {
            detailDTO.setTypeUc("U");
        }
        
        // Set default product unit if not specified
        if (detailDTO.getProductUnit() == null || detailDTO.getProductUnit().isEmpty()) {
            detailDTO.setProductUnit("UC");
        }
        
        LOG.debug("Successfully parsed AZIZA template - Ref: {}, Product: {}, Qty: {}, Unit: {}", 
                  detailDTO.getRef(), detailDTO.getProductName(), detailDTO.getQuantity(), detailDTO.getProductUnit());
    }

    /**
     * Parse GEANT template format (to be implemented)
     */
    private void parseGeantTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        LOG.debug("Parsing GEANT template format for row: {}", row.toString());
        
        // Product reference - from "N° article"
        if (row.has("N° article")) {
            String productRef = row.get("N° article").asText().trim();
            if (!productRef.isEmpty()) {
                detailDTO.setInternalCode(productRef);  // Updated: use internalCode instead of ref
            }
        }

        // EAN/Barcode - from "EAN principal" (updated mapping)
        if (row.has("EAN principal")) {
            String barcode = row.get("EAN principal").asText().trim();
            if (!barcode.isEmpty()) {
                detailDTO.setRef(barcode);  // Updated: EAN goes to ref field
            }
        }

        // Product name/description - from "Libellé" (updated mapping)
        if (row.has("Libellé")) {
            String productName = row.get("Libellé").asText().trim();
            if (!productName.isEmpty()) {
                detailDTO.setProductName(productName);  // Updated: Libellé is the product name
            }
        }
        
        // Quantity - from "Qté"
        if (row.has("Qté")) {
            try {
                String qtyStr = row.get("Qté").asText().trim();
                if (!qtyStr.isEmpty()) {
                    BigDecimal qty = new BigDecimal(qtyStr);
                    detailDTO.setQuantity(qty);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse quantity from GEANT template: {}", row.get("Qté").asText());
            }
        }
        
        // Unit price - from "Prix achat en DT"
        if (row.has("Prix achat en DT")) {
            try {
                String priceStr = row.get("Prix achat en DT").asText().trim();
                if (!priceStr.isEmpty()) {
                    Double price = Double.parseDouble(priceStr.replace(",", "."));
                    detailDTO.setUnitPrice(price);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse unit price from GEANT template: {}", row.get("Prix achat en DT").asText());
            }
        }
        
        // Package count - from "Nb colis" 
        if (row.has("Nb colis")) {
            try {
                String packCountStr = row.get("Nb colis").asText().trim();
                if (!packCountStr.isEmpty()) {
                    BigDecimal packCount = new BigDecimal(packCountStr.replace(",", "."));
                    detailDTO.setPackNb(packCount.intValue());
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse package count from GEANT template: {}", row.get("Nb colis").asText());
            }
        }
        
        // Package conversion base - from "PCB"
        if (row.has("PCB")) {
            try {
                String pcbStr = row.get("PCB").asText().trim();
                if (!pcbStr.isEmpty()) {
                    Double pcb = Double.parseDouble(pcbStr);
                    detailDTO.setPcb(pcb);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse PCB from GEANT template: {}", row.get("PCB").asText());
            }
        }
        
        // Promotion amount - from "Montant promo DT"
        if (row.has("Montant promo DT")) {
            try {
                String promoStr = row.get("Montant promo DT").asText().trim();
                if (!promoStr.isEmpty()) {
                    Double promoAmount = Double.parseDouble(promoStr.replace(",", "."));
                    // Convert promotion amount to percentage if unit price is available
                    if (detailDTO.getUnitPrice() != null && detailDTO.getUnitPrice() > 0) {
                        double discountPercentage = (promoAmount / detailDTO.getUnitPrice()) * 100;
                        detailDTO.setDiscount((int) Math.round(discountPercentage));
                    } else {
                        // Store as flat discount amount
                        detailDTO.setDiscount(promoAmount.intValue());
                    }
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse promotion amount from GEANT template: {}", row.get("Montant promo DT").asText());
            }
        }
        
        // VAT rate - from "TVA"
        if (row.has("TVA")) {
            try {
                String vatStr = row.get("TVA").asText().trim();
                if (!vatStr.isEmpty()) {
                    Double vat = Double.parseDouble(vatStr.replace(",", "."));
                    detailDTO.setTva(vat);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse VAT from GEANT template: {}", row.get("TVA").asText());
            }
        }
        
        // Set product unit as default "UC" for GEANT template
        detailDTO.setProductUnit("UC");
        
        // Set type as default "U" for GEANT template  
        detailDTO.setTypeUc("U");
        
        LOG.debug("Successfully parsed GEANT template - Ref: {}, Product: {}, Qty: {}, Price: {}", 
                  detailDTO.getRef(), detailDTO.getProductName(), detailDTO.getQuantity(), detailDTO.getUnitPrice());
    }

    /**
     * Parse UHD template format (same structure as UCC template)
     */
    private void parseUHDTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        LOG.debug("Parsing UHD template format for row: {}", row.toString());
        
        // UHD template has identical structure to UCC template, so reuse UCC parsing logic
        parseUCCTemplate(row, detailDTO);
        
        LOG.debug("Successfully parsed UHD template using UCC parsing logic - Ref: {}, Product: {}, Qty: {}, Price: {}", 
                  detailDTO.getRef(), detailDTO.getProductName(), detailDTO.getQuantity(), detailDTO.getUnitPrice());
    }

    /**
     * Parse UCC template format
     */
    private void parseUCCTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        LOG.debug("Parsing UCC template format for row: {}", row.toString());
        
        // Product reference - from "N° article"
        if (row.has("N° article")) {
            String productRef = row.get("N° article").asText().trim();
            if (!productRef.isEmpty()) {
                detailDTO.setRef(productRef);
            }
        }
        
        // Product description - from "Libellé" 
        if (row.has("Libellé")) {
            String productName = row.get("Libellé").asText().trim();
            if (!productName.isEmpty()) {
                detailDTO.setProductName(productName);
            }
        }
        
        // EAN/Barcode - from "EAN principal" (can be empty)
        if (row.has("EAN principal")) {
            String barcode = row.get("EAN principal").asText().trim();
            if (!barcode.isEmpty()) {
                detailDTO.setBarcode(barcode);
            }
        }
        
        // Quantity - from "Qté" (includes unit like "560 PCE")
        if (row.has("Qté")) {
            try {
                String qtyStr = row.get("Qté").asText().trim();
                // Remove unit indicators like "PCE", "KG", "L", etc.
                qtyStr = qtyStr.replaceAll("\\s*(PCE|KG|L|ML|G|UC)\\s*$", "").trim();
                if (!qtyStr.isEmpty()) {
                    BigDecimal qty = new BigDecimal(qtyStr);
                    detailDTO.setQuantity(qty);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse quantity from UCC template: {}", row.get("Qté").asText());
            }
        }
        
        // Unit price - from "Prix achat en DIN"
        if (row.has("Prix achat en DIN")) {
            try {
                String priceStr = row.get("Prix achat en DIN").asText().trim();
                if (!priceStr.isEmpty()) {
                    // Handle comma as decimal separator
                    priceStr = priceStr.replace(",", ".");
                    Double price = Double.parseDouble(priceStr);
                    detailDTO.setUnitPrice(price);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse unit price from UCC template: {}", row.get("Prix achat en DIN").asText());
            }
        }
        
        // Package count - from "Colis"
        if (row.has("Colis")) {
            try {
                String packCountStr = row.get("Colis").asText().trim();
                if (!packCountStr.isEmpty()) {
                    Integer packCount = Integer.parseInt(packCountStr);
                    detailDTO.setPackNb(packCount);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse package count from UCC template: {}", row.get("Colis").asText());
            }
        }
        
        // Package conversion base - from "PCB"
        if (row.has("PCB")) {
            try {
                String pcbStr = row.get("PCB").asText().trim();
                if (!pcbStr.isEmpty()) {
                    Double pcb = Double.parseDouble(pcbStr);
                    detailDTO.setPcb(pcb);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse PCB from UCC template: {}", row.get("PCB").asText());
            }
        }
        
        // Discount - from "Mt /% Promo" (can be empty)
        if (row.has("Mt /% Promo")) {
            try {
                String discountStr = row.get("Mt /% Promo").asText().trim();
                if (!discountStr.isEmpty()) {
                    // Handle percentage format like "5%" or "5.00-%"
                    if (discountStr.contains("%")) {
                        discountStr = discountStr.replace("-%", "").replace("%", "").replace(",", ".").trim();
                        if (!discountStr.isEmpty()) {
                            Double discountDouble = Double.parseDouble(discountStr);
                            detailDTO.setDiscount(discountDouble.intValue());
                        }
                    }
                    // Handle absolute amount (if any)
                    else if (discountStr.matches("^[0-9.,]+$")) {
                        discountStr = discountStr.replace(",", ".");
                        Double discountAmount = Double.parseDouble(discountStr);
                        // Convert absolute discount to percentage if unit price is available
                        if (detailDTO.getUnitPrice() != null && detailDTO.getUnitPrice() > 0) {
                            double discountPercentage = (discountAmount / detailDTO.getUnitPrice()) * 100;
                            detailDTO.setDiscount((int) Math.round(discountPercentage));
                        } else {
                            // Store as flat discount amount
                            detailDTO.setDiscount(discountAmount.intValue());
                        }
                    }
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse discount from UCC template: {}", row.get("Mt /% Promo").asText());
            }
        }
        
        // VAT rate - from "TVA" (format like "19%")
        if (row.has("TVA")) {
            try {
                String vatStr = row.get("TVA").asText().trim().replace("%", "");
                if (!vatStr.isEmpty()) {
                    Double vat = Double.parseDouble(vatStr.replace(",", "."));
                    detailDTO.setTva(vat);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse VAT from UCC template: {}", row.get("TVA").asText());
            }
        }
        
        // Extract product unit from quantity field (e.g., "PCE" from "560 PCE")
        if (row.has("Qté")) {
            String qtyStr = row.get("Qté").asText().trim();
            String[] parts = qtyStr.split("\\s+");
            if (parts.length > 1) {
                String unit = parts[parts.length - 1]; // Last part should be the unit
                detailDTO.setProductUnit(unit);
            } else {
                detailDTO.setProductUnit("UC"); // Default unit
            }
        }
        
        // Set type as default "U" for UCC template  
        detailDTO.setTypeUc("U");
        
        LOG.debug("Successfully parsed UCC template - Ref: {}, Product: {}, Qty: {}, Price: {}", 
                  detailDTO.getRef(), detailDTO.getProductName(), detailDTO.getQuantity(), detailDTO.getUnitPrice());
    }

    /**
     * Parse HYPER template format
     */
    private void parseHyperTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        // Internal code - from "N° article"
        if (row.has("N° article")) {
            detailDTO.setInternalCode(row.get("N° article").asText());
        }
        
        // Barcode - from "EAN principal"
        if (row.has("EAN principal")) {
            String barcode = row.get("EAN principal").asText().trim();
            if (!barcode.isEmpty()) {
                detailDTO.setBarcode(barcode);
            }
        }
        
        // Product description - from "Libellé"
        if (row.has("Libellé")) {
            String description = row.get("Libellé").asText().trim();
            if (!description.isEmpty()) {
                detailDTO.setProductName(description);
            }
        }
        
        // Unit price - from "Prix achat en DIN"
        if (row.has("Prix achat en DIN")) {
            try {
                String unitPriceStr = row.get("Prix achat en DIN").asText().trim();
                // Remove currency symbols and normalize decimal separator
                unitPriceStr = unitPriceStr.replace("€", "").replace("DT", "").replace("DIN", "").replace(",", ".").trim();
                if (!unitPriceStr.isEmpty()) {
                    Double unitPrice = Double.parseDouble(unitPriceStr);
                    detailDTO.setUnitPrice(unitPrice);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse unit price: {}", row.get("Prix achat en DIN").asText());
            }
        }
        
        // Quantity - from "Qté"
        if (row.has("Qté")) {
            try {
                String qtyStr = row.get("Qté").asText().trim();
                // Remove unit indicators like "PCE", "KG", etc.
                qtyStr = qtyStr.replaceAll("\\s*(PCE|KG|L|ML|G)\\s*$", "").trim();
                if (!qtyStr.isEmpty()) {
                    BigDecimal qty = new BigDecimal(qtyStr);
                    detailDTO.setQuantity(qty);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse quantity: {}", row.get("Qté").asText());
            }
        }
        
        // Discount - from "Mt /% Promo"
        if (row.has("Mt /% Promo")) {
            try {
                String discountStr = row.get("Mt /% Promo").asText().trim();
                if (!discountStr.isEmpty()) {
                    // Handle percentage format like "5.00-%"
                    if (discountStr.contains("%")) {
                        discountStr = discountStr.replace("-%", "").replace("%", "").replace(",", ".").trim();
                        if (!discountStr.isEmpty()) {
                            Double discountDouble = Double.parseDouble(discountStr);
                            detailDTO.setDiscount(discountDouble.intValue());
                        }
                    }
                    // Handle absolute amount (if any)
                    else if (discountStr.matches("^[0-9.,]+$")) {
                        discountStr = discountStr.replace(",", ".");
                        Double discountAmount = Double.parseDouble(discountStr);
                        // Convert absolute discount to percentage if unit price is available
                        if (detailDTO.getUnitPrice() != null && detailDTO.getUnitPrice() > 0) {
                            double discountPercentage = (discountAmount / detailDTO.getUnitPrice()) * 100;
                            detailDTO.setDiscount((int) Math.round(discountPercentage));
                        } else {
                            // Store as flat discount amount
                            detailDTO.setDiscount(discountAmount.intValue());
                        }
                    }
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse discount: {}", row.get("Mt /% Promo").asText());
            }
        }
        
        // Package count - from "Colis" (stored in packNb field)
        if (row.has("Colis")) {
            try {
                String colisStr = row.get("Colis").asText().trim();
                if (!colisStr.isEmpty()) {
                    Integer colis = Integer.parseInt(colisStr);
                    detailDTO.setPackNb(colis);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse Colis: {}", row.get("Colis").asText());
            }
        }
        
        // PCB (pieces per box/carton) - from "PCB"
        if (row.has("PCB")) {
            try {
                String pcbStr = row.get("PCB").asText().trim();
                if (!pcbStr.isEmpty()) {
                    Double pcb = Double.parseDouble(pcbStr);
                    detailDTO.setPcb(pcb);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse PCB: {}", row.get("PCB").asText());
            }
        }
        
        // TVA (tax rate) - from "TVA"
        if (row.has("TVA")) {
            try {
                String tvaStr = row.get("TVA").asText().trim();
                tvaStr = tvaStr.replace("%", "").trim();
                if (!tvaStr.isEmpty()) {
                    Double tva = Double.parseDouble(tvaStr);
                    detailDTO.setTva(tva);
                }
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse TVA: {}", row.get("TVA").asText());
            }
        }
        
        LOG.debug("Parsed HYPER template for article: {} with price: {} and quantity: {}", 
                 detailDTO.getInternalCode(), detailDTO.getUnitPrice(), detailDTO.getQuantity());
    }

    /**
     * Parse ENTREPOT SFAX template format (to be implemented)
     */
    private void parseEntrepotSfaxTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        // TODO: Implement when ENTREPOT SFAX template format is provided
        LOG.warn("ENTREPOT SFAX template parsing not yet implemented for row: {}", row.toString());
    }

    /**
     * Parse MONOGROS template format (same structure as GEANT template)
     */
    private void parseMonogrosTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        LOG.debug("Parsing MONOGROS template format for row: {}", row.toString());
        
        // MONOGROS template has identical structure to GEANT template, so reuse GEANT parsing logic
        parseGeantTemplate(row, detailDTO);
        
        LOG.debug("Successfully parsed MONOGROS template using GEANT parsing logic - Ref: {}, Product: {}, Qty: {}, Price: {}", 
                  detailDTO.getRef(), detailDTO.getProductName(), detailDTO.getQuantity(), detailDTO.getUnitPrice());
    }

    /**
     * Parse CARREFOUR MARKET template format
     */
    private void parseCarrefourTemplate(JsonNode row, OrderDetailsDTO detailDTO) {
        LOG.debug("Parsing CARREFOUR MARKET template row: {}", row.toString());

        // Set basic product information
        if (row.has("N° article")) {
            detailDTO.setInternalCode(row.get("N° article").asText());
        }

        if (row.has("EAN principal")) {
            detailDTO.setRef(row.get("EAN principal").asText());
        }

        if (row.has("Libellé")) {
            detailDTO.setProductName(row.get("Libellé").asText());
        }

        // Set quantity information
        if (row.has("Qté")) {
            String qtyText = row.get("Qté").asText();
            // Handle format like "240 PCE" - extract the number
            String numericPart = qtyText.replaceAll("[^0-9.]", "");
            if (!numericPart.isEmpty()) {
                try {
                    detailDTO.setQuantity(new BigDecimal(numericPart));
                } catch (NumberFormatException e) {
                    LOG.warn("Could not parse quantity '{}' for CARREFOUR template", qtyText);
                    detailDTO.setQuantity(BigDecimal.ZERO);
                }
            }
        }

        // Set price information
        if (row.has("Prix achat en DIN")) {
            String priceText = row.get("Prix achat en DIN").asText();
            // Handle format like "2,053" - replace comma with dot
            String normalizedPrice = priceText.replace(",", ".");
            try {
                detailDTO.setUnitPrice(Double.parseDouble(normalizedPrice));
            } catch (NumberFormatException e) {
                LOG.warn("Could not parse price '{}' for CARREFOUR template", priceText);
                detailDTO.setUnitPrice(0.0);
            }
        }

        // Set packaging information
        if (row.has("Colis")) {
            // Store in order line JSON since there's no direct field
            // detailDTO.setPacksNb(row.get("Colis").asInt());
        }

        if (row.has("PCB")) {
            detailDTO.setPcb(Double.valueOf(row.get("PCB").asInt()));
        }

        // Handle promotion and TVA information - store in order line JSON
        String promoText = "";
        if (row.has("Mt /% Promo")) {
            promoText = row.get("Mt /% Promo").asText();
        }

        String tvaText = "";
        if (row.has("TVA")) {
            tvaText = row.get("TVA").asText();
        }

        // Set default values
        detailDTO.setAvailability(true);
        detailDTO.setDiscountStatus(null);
        detailDTO.setPriceStatus(null);
        detailDTO.setQuantityStatus(null);
        detailDTO.setProductStatus(null);

        // Create order line JSON with CARREFOUR-specific information
        java.util.Map<String, Object> lineInfo = new java.util.HashMap<>();
        lineInfo.put("template_type", "CARREFOUR_MARKET");
        lineInfo.put("promotion", promoText);
        lineInfo.put("tva", tvaText);
        if (row.has("Colis")) {
            lineInfo.put("colis", row.get("Colis").asInt());
        }
        lineInfo.put("original_data", row.toString());

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            detailDTO.setOrderLineJson(objectMapper.writeValueAsString(lineInfo));
        } catch (Exception e) {
            LOG.warn("Could not serialize CARREFOUR order line info: {}", e.getMessage());
            detailDTO.setOrderLineJson("{\"template_type\":\"CARREFOUR_MARKET\"}");
        }

        LOG.debug("Parsed CARREFOUR MARKET template - Product: {}, Quantity: {}, Price: {}",
                detailDTO.getProductName(), detailDTO.getQuantity(), detailDTO.getUnitPrice());
    }

    /**
     * {@code GET  /orders/batches/batchId/brands/:brandId} : get Order by batchId and brandId.
     *
     * @param batchId the id of the batch.
     * @param brandId the id of the brand.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the orderDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/batches/{batchId}/brands/{brandId}")
    public ResponseEntity<OrderDTO> getBatchOrderByBrand(
        @PathVariable Long batchId,
        @PathVariable Long brandId
    ) {
        LOG.debug("REST request to get Order by batchId: {}, brandId: {}", batchId, brandId);
        Optional<OrderDTO> orderDTO = orderService.findByBrandAndBatche(batchId, brandId);
        return ResponseUtil.wrapOrNotFound(orderDTO);
    }


    /**
     * {@code GET  /orders/employee/employeeId/batches/:batchesId} : get Order by employeeId and batchesId.
     *
     * @param employeeId the id of the batch.
     * @param batchesId    the id of the batche.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the orderDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/employee/{employeeId}/batche/{batchesId}")
    public List<OrderDTO> getBatchOrdersByEmployee(
        @PathVariable Long employeeId,
        @PathVariable Long batchesId
    ) {
        LOG.debug("REST request to get Order by employeeId: {}, batchesId: {}", employeeId, batchesId);
        if (employeeId == null || batchesId == null) {
            throw new BadRequestAlertException("Employee ID and batche ID must not be null", ENTITY_NAME, "idnull");
        }
        List<Long> employeeGmsBrands = employeeGmsBrandsService.findBrandsByEmployee(employeeId);

        List<OrderDTO> ordersDTO = orderService.findByBatcheAndBrands(batchesId, employeeGmsBrands);
        return ordersDTO;
    }

    /**
     * {@code GET  /client-brand/{fiscalId}} : Get client and brand information by fiscal ID.
     *
     * @param fiscalId the fiscal ID to search for.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the client and brand information, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/client-brand/{fiscalId}")
    public ResponseEntity<ClientBrandInfoDTO> getClientAndBrandByFiscalId(@PathVariable String fiscalId) {
        LOG.debug("REST request to get client and brand info by fiscal ID: {}", fiscalId);
        ClientBrandInfoDTO result = orderService.getClientAndBrandByFiscalId(fiscalId);
        if (result != null) {
            return ResponseEntity.ok().body(result);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * {@code GET  /next-order-number/{companyId}} : Get the next order number for a company.
     *
     * @param companyId the company ID.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the batch ID and next order number.
     */
    @GetMapping("/next-order-number/{companyId}")
    public ResponseEntity<BatchOrderInfoDTO> getNextOrderNumber(@PathVariable Long companyId) {
        LOG.debug("REST request to get next order number for company ID: {}", companyId);
        BatchOrderInfoDTO result = orderService.getNextOrderNumber(companyId);
        return ResponseEntity.ok().body(result);
    }

    /**
     * {@code POST  /increment-order-number/{batchId}} : Increment the order number for a batch.
     *
     * @param batchId the batch ID.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)}.
     */
    @PostMapping("/increment-order-number/{batchId}")
    public ResponseEntity<Void> incrementOrderNumber(@PathVariable Long batchId) {
        LOG.debug("REST request to increment order number for batch ID: {}", batchId);
        orderService.incrementOrderNumber(batchId);
        return ResponseEntity.ok().build();
    }

    /**
     * {@code GET  /company-id/{companyName}} : Get company ID by company name.
     *
     * @param companyName the company name.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the company ID, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/company-id/{companyName}")
    public ResponseEntity<Long> getCompanyIdByName(@PathVariable String companyName) {
        LOG.debug("REST request to get company ID by name: {}", companyName);
        Optional<Long> companyId = orderService.getCompanyIdByName(companyName);
        if (companyId.isPresent()) {
            return ResponseEntity.ok().body(companyId.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * {@code GET  /by-brand/{brandId}} : Get orders by brand ID.
     *
     * @param brandId the brand ID.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of orders in body.
     */
    @GetMapping("/by-brand/{brandId}")
    public ResponseEntity<List<OrderDTO>> getOrdersByBrand(@PathVariable Long brandId) {
        LOG.debug("REST request to get Orders by brand ID: {}", brandId);
        List<OrderDTO> orders = orderService.getOrdersByBrandId(brandId);
        return ResponseEntity.ok().body(orders);
    }

    /**
     * {@code GET  /by-company/{company}} : Get orders by company name.
     *
     * @param company the company name.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of orders in body.
     */
    @GetMapping("/by-company/{company}")
    public ResponseEntity<List<OrderDTO>> getOrdersByCompany(@PathVariable String company) {
        LOG.debug("REST request to get Orders by company: {}", company);
        List<OrderDTO> orders = orderService.getOrdersByCompany(company);
        return ResponseEntity.ok().body(orders);
    }
}
