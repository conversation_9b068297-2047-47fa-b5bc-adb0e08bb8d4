package com.dq.lilas.service;

import com.dq.lilas.service.dto.GmsClientsDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.GmsClients}.
 */
public interface GmsClientsService {
    /**
     * Save a gmsClients.
     *
     * @param gmsClientsDTO the entity to save.
     * @return the persisted entity.
     */
    GmsClientsDTO save(GmsClientsDTO gmsClientsDTO);

    /**
     * Updates a gmsClients.
     *
     * @param gmsClientsDTO the entity to update.
     * @return the persisted entity.
     */
    GmsClientsDTO update(GmsClientsDTO gmsClientsDTO);

    /**
     * Partially updates a gmsClients.
     *
     * @param gmsClientsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<GmsClientsDTO> partialUpdate(GmsClientsDTO gmsClientsDTO);

    /**
     * Get all the gmsClients.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<GmsClientsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" gmsClients.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<GmsClientsDTO> findOne(Long id);

    /**
     * Delete the "id" gmsClients.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Get GmsClients by fiscal ID.
     *
     * @param fiscaleId the fiscal ID to search for.
     * @return the entity if found.
     */
    Optional<GmsClientsDTO> findByFiscaleId(String fiscaleId);
}
