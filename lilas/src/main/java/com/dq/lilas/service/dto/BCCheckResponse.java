package com.dq.lilas.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public class BCCheckResponse {
        private String status;

        @JsonProperty("header_info")
        private Map<String, String> headerInfo;

        @JsonProperty("delivery_info")
        private Map<String, String> deliveryInfo;

        @JsonProperty("table_info")
        private Map<String, List<Map<String, String>>> tableInfo;

        @JsonProperty("footer_info")
        private Map<String, String> footerInfo;
    }
