# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.dq.lilas: DEBUG

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ****************************************
    username: postgres
    password: xpress11
    hikari:
      poolName: Hikari
      auto-commit: false
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev, faker
    # Skip checksum validation for development to avoid issues when modifying data files
    drop-first: false
    # Skip checksum validation to prevent errors during development
    change-log: classpath:config/liquibase/master.xml
    # Skip checksum validation during development
    test-rollback-on-update: false
  mail:
    host: smtp.gmail.com
    port: 465
    username: <EMAIL>
    password: yxwiklzzubuqziwz
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
    # Alternative: Use MailHog for local testing
    # host: localhost
    # port: 1025
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false
  jpa:
    hibernate:
      ddl-auto: update

server:
  port: 8080
  # make sure requests the proxy uri instead of the server one
  forward-headers-strategy: native

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  cache: # Cache configuration
    ehcache: # Ehcache configuration
      time-to-live-seconds: 3600 # By default objects stay 1 hour in the cache
      max-entries: 100 # Number of objects in each cache entry
  # CORS is only enabled by default with the "dev" profile
  cors:
    # Allow Ionic for JHipster by default (* no longer allowed in Spring Boot 2.4+)
    allowed-origins: 'http://localhost:8100,https://localhost:8100,http://localhost:4200,https://localhost:4200'
    # Enable CORS when running in GitHub Codespaces
    allowed-origin-patterns: 'https://*.githubpreview.dev'
    allowed-methods: '*'
    allowed-headers: '*'
    exposed-headers: 'Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params'
    allow-credentials: true
    max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: YjFkNjFiMTY2YzQ2MmFjODM0ZDQwNjBmMTU1MzhiZWFiMzRmZDBkNjU2M2RlNTg5YjM0M2Q2Njg3NWVkNWNlMmEyZjNiNWJjMWQxMWUxYzU5N2MyNzA5YWFlMGMzMGUxZDZmMGNhMDA5OGVjZWRhYzkxNDMwMGQzZDVlNmQwMDE=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  mail: # specific JHipster mail property, for standard properties see MailProperties
    base-url: http://127.0.0.1:8080
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
azure:
  graph:
    client-id: 0c45619b-508e-4b62-ab28-44cd2dba6312
    tenant-id: 02a0ba1f-49da-4a0d-80c5-0c5f3784e72b
    client-secret: ****************************************
    scope: https://graph.microsoft.com/.default
    webhook:
      url: https://c6bbaeb4d5f0.ngrok-free.app/api/notifications
      notification-secret: ********************************
      auto-renew: true

ocr:
  url: http://*************:8000/api/capture/

alfresco:
  url: http://*************:8500/api/alfresco/cmis/documents/create
  directory-path: /
  default-model-id: 1
