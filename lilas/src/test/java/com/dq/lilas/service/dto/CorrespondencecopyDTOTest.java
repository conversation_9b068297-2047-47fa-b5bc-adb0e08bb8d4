package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class CorrespondencecopyDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(CorrespondencecopyDTO.class);
        CorrespondencecopyDTO correspondencecopyDTO1 = new CorrespondencecopyDTO();
        correspondencecopyDTO1.setId(1L);
        CorrespondencecopyDTO correspondencecopyDTO2 = new CorrespondencecopyDTO();
        assertThat(correspondencecopyDTO1).isNotEqualTo(correspondencecopyDTO2);
        correspondencecopyDTO2.setId(correspondencecopyDTO1.getId());
        assertThat(correspondencecopyDTO1).isEqualTo(correspondencecopyDTO2);
        correspondencecopyDTO2.setId(2L);
        assertThat(correspondencecopyDTO1).isNotEqualTo(correspondencecopyDTO2);
        correspondencecopyDTO1.setId(null);
        assertThat(correspondencecopyDTO1).isNotEqualTo(correspondencecopyDTO2);
    }
}
