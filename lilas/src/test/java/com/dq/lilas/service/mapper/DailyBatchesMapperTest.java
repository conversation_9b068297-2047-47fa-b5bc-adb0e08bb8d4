package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.DailyBatchesAsserts.*;
import static com.dq.lilas.domain.DailyBatchesTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DailyBatchesMapperTest {

    private DailyBatchesMapper dailyBatchesMapper;

    @BeforeEach
    void setUp() {
        dailyBatchesMapper = new DailyBatchesMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getDailyBatchesSample1();
        var actual = dailyBatchesMapper.toEntity(dailyBatchesMapper.toDto(expected));
        assertDailyBatchesAllPropertiesEquals(expected, actual);
    }
}
