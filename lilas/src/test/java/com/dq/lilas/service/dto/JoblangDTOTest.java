package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class JoblangDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(JoblangDTO.class);
        JoblangDTO joblangDTO1 = new JoblangDTO();
        joblangDTO1.setId(1L);
        JoblangDTO joblangDTO2 = new JoblangDTO();
        assertThat(joblangDTO1).isNotEqualTo(joblangDTO2);
        joblangDTO2.setId(joblangDTO1.getId());
        assertThat(joblangDTO1).isEqualTo(joblangDTO2);
        joblangDTO2.setId(2L);
        assertThat(joblangDTO1).isNotEqualTo(joblangDTO2);
        joblangDTO1.setId(null);
        assertThat(joblangDTO1).isNotEqualTo(joblangDTO2);
    }
}
