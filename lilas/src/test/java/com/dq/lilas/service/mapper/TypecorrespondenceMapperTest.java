package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.TypecorrespondenceAsserts.*;
import static com.dq.lilas.domain.TypecorrespondenceTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TypecorrespondenceMapperTest {

    private TypecorrespondenceMapper typecorrespondenceMapper;

    @BeforeEach
    void setUp() {
        typecorrespondenceMapper = new TypecorrespondenceMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTypecorrespondenceSample1();
        var actual = typecorrespondenceMapper.toEntity(typecorrespondenceMapper.toDto(expected));
        assertTypecorrespondenceAllPropertiesEquals(expected, actual);
    }
}
