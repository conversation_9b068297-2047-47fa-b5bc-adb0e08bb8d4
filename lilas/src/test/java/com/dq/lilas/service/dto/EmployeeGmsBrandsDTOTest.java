package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeGmsBrandsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeGmsBrandsDTO.class);
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO1 = new EmployeeGmsBrandsDTO();
        employeeGmsBrandsDTO1.setId(1L);
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO2 = new EmployeeGmsBrandsDTO();
        assertThat(employeeGmsBrandsDTO1).isNotEqualTo(employeeGmsBrandsDTO2);
        employeeGmsBrandsDTO2.setId(employeeGmsBrandsDTO1.getId());
        assertThat(employeeGmsBrandsDTO1).isEqualTo(employeeGmsBrandsDTO2);
        employeeGmsBrandsDTO2.setId(2L);
        assertThat(employeeGmsBrandsDTO1).isNotEqualTo(employeeGmsBrandsDTO2);
        employeeGmsBrandsDTO1.setId(null);
        assertThat(employeeGmsBrandsDTO1).isNotEqualTo(employeeGmsBrandsDTO2);
    }
}
