package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.DailyStockAsserts.*;
import static com.dq.lilas.domain.DailyStockTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DailyStockMapperTest {

    private DailyStockMapper dailyStockMapper;

    @BeforeEach
    void setUp() {
        dailyStockMapper = new DailyStockMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getDailyStockSample1();
        var actual = dailyStockMapper.toEntity(dailyStockMapper.toDto(expected));
        assertDailyStockAllPropertiesEquals(expected, actual);
    }
}
