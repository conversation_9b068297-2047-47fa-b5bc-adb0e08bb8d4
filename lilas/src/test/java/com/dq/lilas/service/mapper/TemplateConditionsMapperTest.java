package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.TemplateConditionsAsserts.*;
import static com.dq.lilas.domain.TemplateConditionsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TemplateConditionsMapperTest {

    private TemplateConditionsMapper templateConditionsMapper;

    @BeforeEach
    void setUp() {
        templateConditionsMapper = new TemplateConditionsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTemplateConditionsSample1();
        var actual = templateConditionsMapper.toEntity(templateConditionsMapper.toDto(expected));
        assertTemplateConditionsAllPropertiesEquals(expected, actual);
    }
}
