package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class GmsClientsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(GmsClientsDTO.class);
        GmsClientsDTO gmsClientsDTO1 = new GmsClientsDTO();
        gmsClientsDTO1.setId(1L);
        GmsClientsDTO gmsClientsDTO2 = new GmsClientsDTO();
        assertThat(gmsClientsDTO1).isNotEqualTo(gmsClientsDTO2);
        gmsClientsDTO2.setId(gmsClientsDTO1.getId());
        assertThat(gmsClientsDTO1).isEqualTo(gmsClientsDTO2);
        gmsClientsDTO2.setId(2L);
        assertThat(gmsClientsDTO1).isNotEqualTo(gmsClientsDTO2);
        gmsClientsDTO1.setId(null);
        assertThat(gmsClientsDTO1).isNotEqualTo(gmsClientsDTO2);
    }
}
