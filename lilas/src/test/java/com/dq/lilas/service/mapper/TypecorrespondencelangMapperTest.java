package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.TypecorrespondencelangAsserts.*;
import static com.dq.lilas.domain.TypecorrespondencelangTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TypecorrespondencelangMapperTest {

    private TypecorrespondencelangMapper typecorrespondencelangMapper;

    @BeforeEach
    void setUp() {
        typecorrespondencelangMapper = new TypecorrespondencelangMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTypecorrespondencelangSample1();
        var actual = typecorrespondencelangMapper.toEntity(typecorrespondencelangMapper.toDto(expected));
        assertTypecorrespondencelangAllPropertiesEquals(expected, actual);
    }
}
