package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ProductsListDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ProductsListDTO.class);
        ProductsListDTO productsListDTO1 = new ProductsListDTO();
        productsListDTO1.setId(1L);
        ProductsListDTO productsListDTO2 = new ProductsListDTO();
        assertThat(productsListDTO1).isNotEqualTo(productsListDTO2);
        productsListDTO2.setId(productsListDTO1.getId());
        assertThat(productsListDTO1).isEqualTo(productsListDTO2);
        productsListDTO2.setId(2L);
        assertThat(productsListDTO1).isNotEqualTo(productsListDTO2);
        productsListDTO1.setId(null);
        assertThat(productsListDTO1).isNotEqualTo(productsListDTO2);
    }
}
