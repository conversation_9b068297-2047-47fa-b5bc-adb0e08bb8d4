package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.ActionAsserts.*;
import static com.dq.lilas.domain.ActionTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ActionMapperTest {

    private ActionMapper actionMapper;

    @BeforeEach
    void setUp() {
        actionMapper = new ActionMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getActionSample1();
        var actual = actionMapper.toEntity(actionMapper.toDto(expected));
        assertActionAllPropertiesEquals(expected, actual);
    }
}
