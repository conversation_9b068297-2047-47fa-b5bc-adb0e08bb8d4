package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.MailsAsserts.*;
import static com.dq.lilas.domain.MailsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MailsMapperTest {

    private MailsMapper mailsMapper;

    @BeforeEach
    void setUp() {
        mailsMapper = new MailsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getMailsSample1();
        var actual = mailsMapper.toEntity(mailsMapper.toDto(expected));
        assertMailsAllPropertiesEquals(expected, actual);
    }
}
