package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ActionlangDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ActionlangDTO.class);
        ActionlangDTO actionlangDTO1 = new ActionlangDTO();
        actionlangDTO1.setId(1L);
        ActionlangDTO actionlangDTO2 = new ActionlangDTO();
        assertThat(actionlangDTO1).isNotEqualTo(actionlangDTO2);
        actionlangDTO2.setId(actionlangDTO1.getId());
        assertThat(actionlangDTO1).isEqualTo(actionlangDTO2);
        actionlangDTO2.setId(2L);
        assertThat(actionlangDTO1).isNotEqualTo(actionlangDTO2);
        actionlangDTO1.setId(null);
        assertThat(actionlangDTO1).isNotEqualTo(actionlangDTO2);
    }
}
