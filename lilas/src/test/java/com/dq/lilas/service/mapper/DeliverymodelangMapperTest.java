package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.DeliverymodelangAsserts.*;
import static com.dq.lilas.domain.DeliverymodelangTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DeliverymodelangMapperTest {

    private DeliverymodelangMapper deliverymodelangMapper;

    @BeforeEach
    void setUp() {
        deliverymodelangMapper = new DeliverymodelangMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getDeliverymodelangSample1();
        var actual = deliverymodelangMapper.toEntity(deliverymodelangMapper.toDto(expected));
        assertDeliverymodelangAllPropertiesEquals(expected, actual);
    }
}
