package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.TransferAsserts.*;
import static com.dq.lilas.domain.TransferTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TransferMapperTest {

    private TransferMapper transferMapper;

    @BeforeEach
    void setUp() {
        transferMapper = new TransferMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTransferSample1();
        var actual = transferMapper.toEntity(transferMapper.toDto(expected));
        assertTransferAllPropertiesEquals(expected, actual);
    }
}
