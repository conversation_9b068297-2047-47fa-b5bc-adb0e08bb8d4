package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmailsNotificationsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmailsNotificationsDTO.class);
        EmailsNotificationsDTO emailsNotificationsDTO1 = new EmailsNotificationsDTO();
        emailsNotificationsDTO1.setId(1L);
        EmailsNotificationsDTO emailsNotificationsDTO2 = new EmailsNotificationsDTO();
        assertThat(emailsNotificationsDTO1).isNotEqualTo(emailsNotificationsDTO2);
        emailsNotificationsDTO2.setId(emailsNotificationsDTO1.getId());
        assertThat(emailsNotificationsDTO1).isEqualTo(emailsNotificationsDTO2);
        emailsNotificationsDTO2.setId(2L);
        assertThat(emailsNotificationsDTO1).isNotEqualTo(emailsNotificationsDTO2);
        emailsNotificationsDTO1.setId(null);
        assertThat(emailsNotificationsDTO1).isNotEqualTo(emailsNotificationsDTO2);
    }
}
