package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.CadencierAsserts.*;
import static com.dq.lilas.domain.CadencierTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CadencierMapperTest {

    private CadencierMapper cadencierMapper;

    @BeforeEach
    void setUp() {
        cadencierMapper = new CadencierMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getCadencierSample1();
        var actual = cadencierMapper.toEntity(cadencierMapper.toDto(expected));
        assertCadencierAllPropertiesEquals(expected, actual);
    }
}
