package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.EmailsNotificationsAsserts.*;
import static com.dq.lilas.domain.EmailsNotificationsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EmailsNotificationsMapperTest {

    private EmailsNotificationsMapper emailsNotificationsMapper;

    @BeforeEach
    void setUp() {
        emailsNotificationsMapper = new EmailsNotificationsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getEmailsNotificationsSample1();
        var actual = emailsNotificationsMapper.toEntity(emailsNotificationsMapper.toDto(expected));
        assertEmailsNotificationsAllPropertiesEquals(expected, actual);
    }
}
