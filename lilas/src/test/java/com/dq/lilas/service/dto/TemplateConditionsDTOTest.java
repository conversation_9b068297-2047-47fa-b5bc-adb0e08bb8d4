package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TemplateConditionsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TemplateConditionsDTO.class);
        TemplateConditionsDTO templateConditionsDTO1 = new TemplateConditionsDTO();
        templateConditionsDTO1.setId(1L);
        TemplateConditionsDTO templateConditionsDTO2 = new TemplateConditionsDTO();
        assertThat(templateConditionsDTO1).isNotEqualTo(templateConditionsDTO2);
        templateConditionsDTO2.setId(templateConditionsDTO1.getId());
        assertThat(templateConditionsDTO1).isEqualTo(templateConditionsDTO2);
        templateConditionsDTO2.setId(2L);
        assertThat(templateConditionsDTO1).isNotEqualTo(templateConditionsDTO2);
        templateConditionsDTO1.setId(null);
        assertThat(templateConditionsDTO1).isNotEqualTo(templateConditionsDTO2);
    }
}
