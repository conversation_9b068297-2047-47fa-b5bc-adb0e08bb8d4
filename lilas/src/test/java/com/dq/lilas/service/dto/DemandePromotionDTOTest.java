package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DemandePromotionDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(DemandePromotionDTO.class);
        DemandePromotionDTO demandePromotionDTO1 = new DemandePromotionDTO();
        demandePromotionDTO1.setId(1L);
        DemandePromotionDTO demandePromotionDTO2 = new DemandePromotionDTO();
        assertThat(demandePromotionDTO1).isNotEqualTo(demandePromotionDTO2);
        demandePromotionDTO2.setId(demandePromotionDTO1.getId());
        assertThat(demandePromotionDTO1).isEqualTo(demandePromotionDTO2);
        demandePromotionDTO2.setId(2L);
        assertThat(demandePromotionDTO1).isNotEqualTo(demandePromotionDTO2);
        demandePromotionDTO1.setId(null);
        assertThat(demandePromotionDTO1).isNotEqualTo(demandePromotionDTO2);
    }
}
