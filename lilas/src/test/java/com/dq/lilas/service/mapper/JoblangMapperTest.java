package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.JoblangAsserts.*;
import static com.dq.lilas.domain.JoblangTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class JoblangMapperTest {

    private JoblangMapper joblangMapper;

    @BeforeEach
    void setUp() {
        joblangMapper = new JoblangMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getJoblangSample1();
        var actual = joblangMapper.toEntity(joblangMapper.toDto(expected));
        assertJoblangAllPropertiesEquals(expected, actual);
    }
}
