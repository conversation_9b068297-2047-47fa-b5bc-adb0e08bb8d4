package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.AttachementAsserts.*;
import static com.dq.lilas.domain.AttachementTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AttachementMapperTest {

    private AttachementMapper attachementMapper;

    @BeforeEach
    void setUp() {
        attachementMapper = new AttachementMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAttachementSample1();
        var actual = attachementMapper.toEntity(attachementMapper.toDto(expected));
        assertAttachementAllPropertiesEquals(expected, actual);
    }
}
