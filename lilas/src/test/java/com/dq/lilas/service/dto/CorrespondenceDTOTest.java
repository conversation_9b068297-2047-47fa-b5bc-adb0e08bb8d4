package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class CorrespondenceDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(CorrespondenceDTO.class);
        CorrespondenceDTO correspondenceDTO1 = new CorrespondenceDTO();
        correspondenceDTO1.setId(1L);
        CorrespondenceDTO correspondenceDTO2 = new CorrespondenceDTO();
        assertThat(correspondenceDTO1).isNotEqualTo(correspondenceDTO2);
        correspondenceDTO2.setId(correspondenceDTO1.getId());
        assertThat(correspondenceDTO1).isEqualTo(correspondenceDTO2);
        correspondenceDTO2.setId(2L);
        assertThat(correspondenceDTO1).isNotEqualTo(correspondenceDTO2);
        correspondenceDTO1.setId(null);
        assertThat(correspondenceDTO1).isNotEqualTo(correspondenceDTO2);
    }
}
