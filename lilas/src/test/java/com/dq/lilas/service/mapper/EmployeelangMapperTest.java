package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.EmployeelangAsserts.*;
import static com.dq.lilas.domain.EmployeelangTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EmployeelangMapperTest {

    private EmployeelangMapper employeelangMapper;

    @BeforeEach
    void setUp() {
        employeelangMapper = new EmployeelangMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getEmployeelangSample1();
        var actual = employeelangMapper.toEntity(employeelangMapper.toDto(expected));
        assertEmployeelangAllPropertiesEquals(expected, actual);
    }
}
