package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.EmployeeGmsBrandsAsserts.*;
import static com.dq.lilas.domain.EmployeeGmsBrandsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EmployeeGmsBrandsMapperTest {

    private EmployeeGmsBrandsMapper employeeGmsBrandsMapper;

    @BeforeEach
    void setUp() {
        employeeGmsBrandsMapper = new EmployeeGmsBrandsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getEmployeeGmsBrandsSample1();
        var actual = employeeGmsBrandsMapper.toEntity(employeeGmsBrandsMapper.toDto(expected));
        assertEmployeeGmsBrandsAllPropertiesEquals(expected, actual);
    }
}
