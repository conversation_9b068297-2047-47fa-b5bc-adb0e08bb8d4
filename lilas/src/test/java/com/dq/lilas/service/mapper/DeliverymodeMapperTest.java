package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.DeliverymodeAsserts.*;
import static com.dq.lilas.domain.DeliverymodeTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DeliverymodeMapperTest {

    private DeliverymodeMapper deliverymodeMapper;

    @BeforeEach
    void setUp() {
        deliverymodeMapper = new DeliverymodeMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getDeliverymodeSample1();
        var actual = deliverymodeMapper.toEntity(deliverymodeMapper.toDto(expected));
        assertDeliverymodeAllPropertiesEquals(expected, actual);
    }
}
