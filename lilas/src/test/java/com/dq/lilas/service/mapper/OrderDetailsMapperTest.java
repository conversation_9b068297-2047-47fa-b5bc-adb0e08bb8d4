package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.OrderDetailsAsserts.*;
import static com.dq.lilas.domain.OrderDetailsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class OrderDetailsMapperTest {

    private OrderDetailsMapper orderDetailsMapper;

    @BeforeEach
    void setUp() {
        orderDetailsMapper = new OrderDetailsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getOrderDetailsSample1();
        var actual = orderDetailsMapper.toEntity(orderDetailsMapper.toDto(expected));
        assertOrderDetailsAllPropertiesEquals(expected, actual);
    }
}
