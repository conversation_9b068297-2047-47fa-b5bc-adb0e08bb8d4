package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.CorrespondencecopyAsserts.*;
import static com.dq.lilas.domain.CorrespondencecopyTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CorrespondencecopyMapperTest {

    private CorrespondencecopyMapper correspondencecopyMapper;

    @BeforeEach
    void setUp() {
        correspondencecopyMapper = new CorrespondencecopyMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getCorrespondencecopySample1();
        var actual = correspondencecopyMapper.toEntity(correspondencecopyMapper.toDto(expected));
        assertCorrespondencecopyAllPropertiesEquals(expected, actual);
    }
}
