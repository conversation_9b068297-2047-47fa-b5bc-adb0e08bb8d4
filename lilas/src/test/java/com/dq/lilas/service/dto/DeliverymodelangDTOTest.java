package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DeliverymodelangDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(DeliverymodelangDTO.class);
        DeliverymodelangDTO deliverymodelangDTO1 = new DeliverymodelangDTO();
        deliverymodelangDTO1.setId(1L);
        DeliverymodelangDTO deliverymodelangDTO2 = new DeliverymodelangDTO();
        assertThat(deliverymodelangDTO1).isNotEqualTo(deliverymodelangDTO2);
        deliverymodelangDTO2.setId(deliverymodelangDTO1.getId());
        assertThat(deliverymodelangDTO1).isEqualTo(deliverymodelangDTO2);
        deliverymodelangDTO2.setId(2L);
        assertThat(deliverymodelangDTO1).isNotEqualTo(deliverymodelangDTO2);
        deliverymodelangDTO1.setId(null);
        assertThat(deliverymodelangDTO1).isNotEqualTo(deliverymodelangDTO2);
    }
}
