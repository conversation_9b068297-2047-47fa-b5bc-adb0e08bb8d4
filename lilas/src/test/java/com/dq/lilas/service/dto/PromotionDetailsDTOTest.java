package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromotionDetailsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromotionDetailsDTO.class);
        PromotionDetailsDTO promotionDetailsDTO1 = new PromotionDetailsDTO();
        promotionDetailsDTO1.setId(1L);
        PromotionDetailsDTO promotionDetailsDTO2 = new PromotionDetailsDTO();
        assertThat(promotionDetailsDTO1).isNotEqualTo(promotionDetailsDTO2);
        promotionDetailsDTO2.setId(promotionDetailsDTO1.getId());
        assertThat(promotionDetailsDTO1).isEqualTo(promotionDetailsDTO2);
        promotionDetailsDTO2.setId(2L);
        assertThat(promotionDetailsDTO1).isNotEqualTo(promotionDetailsDTO2);
        promotionDetailsDTO1.setId(null);
        assertThat(promotionDetailsDTO1).isNotEqualTo(promotionDetailsDTO2);
    }
}
