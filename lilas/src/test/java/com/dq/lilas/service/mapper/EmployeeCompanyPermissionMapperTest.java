package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.EmployeeCompanyPermissionAsserts.*;
import static com.dq.lilas.domain.EmployeeCompanyPermissionTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EmployeeCompanyPermissionMapperTest {

    private EmployeeCompanyPermissionMapper employeeCompanyPermissionMapper;

    @BeforeEach
    void setUp() {
        employeeCompanyPermissionMapper = new EmployeeCompanyPermissionMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getEmployeeCompanyPermissionSample1();
        var actual = employeeCompanyPermissionMapper.toEntity(employeeCompanyPermissionMapper.toDto(expected));
        assertEmployeeCompanyPermissionAllPropertiesEquals(expected, actual);
    }
}
