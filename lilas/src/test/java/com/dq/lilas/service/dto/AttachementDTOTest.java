package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AttachementDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AttachementDTO.class);
        AttachementDTO attachementDTO1 = new AttachementDTO();
        attachementDTO1.setId(1L);
        AttachementDTO attachementDTO2 = new AttachementDTO();
        assertThat(attachementDTO1).isNotEqualTo(attachementDTO2);
        attachementDTO2.setId(attachementDTO1.getId());
        assertThat(attachementDTO1).isEqualTo(attachementDTO2);
        attachementDTO2.setId(2L);
        assertThat(attachementDTO1).isNotEqualTo(attachementDTO2);
        attachementDTO1.setId(null);
        assertThat(attachementDTO1).isNotEqualTo(attachementDTO2);
    }
}
