package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.ProductsListAsserts.*;
import static com.dq.lilas.domain.ProductsListTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ProductsListMapperTest {

    private ProductsListMapper productsListMapper;

    @BeforeEach
    void setUp() {
        productsListMapper = new ProductsListMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getProductsListSample1();
        var actual = productsListMapper.toEntity(productsListMapper.toDto(expected));
        assertProductsListAllPropertiesEquals(expected, actual);
    }
}
