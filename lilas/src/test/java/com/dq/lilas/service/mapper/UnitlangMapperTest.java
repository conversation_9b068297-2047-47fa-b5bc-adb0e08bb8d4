package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.UnitlangAsserts.*;
import static com.dq.lilas.domain.UnitlangTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class UnitlangMapperTest {

    private UnitlangMapper unitlangMapper;

    @BeforeEach
    void setUp() {
        unitlangMapper = new UnitlangMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getUnitlangSample1();
        var actual = unitlangMapper.toEntity(unitlangMapper.toDto(expected));
        assertUnitlangAllPropertiesEquals(expected, actual);
    }
}
