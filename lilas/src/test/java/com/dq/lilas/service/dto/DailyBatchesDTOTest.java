package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DailyBatchesDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(DailyBatchesDTO.class);
        DailyBatchesDTO dailyBatchesDTO1 = new DailyBatchesDTO();
        dailyBatchesDTO1.setId(1L);
        DailyBatchesDTO dailyBatchesDTO2 = new DailyBatchesDTO();
        assertThat(dailyBatchesDTO1).isNotEqualTo(dailyBatchesDTO2);
        dailyBatchesDTO2.setId(dailyBatchesDTO1.getId());
        assertThat(dailyBatchesDTO1).isEqualTo(dailyBatchesDTO2);
        dailyBatchesDTO2.setId(2L);
        assertThat(dailyBatchesDTO1).isNotEqualTo(dailyBatchesDTO2);
        dailyBatchesDTO1.setId(null);
        assertThat(dailyBatchesDTO1).isNotEqualTo(dailyBatchesDTO2);
    }
}
