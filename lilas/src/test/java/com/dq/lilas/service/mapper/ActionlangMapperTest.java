package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.ActionlangAsserts.*;
import static com.dq.lilas.domain.ActionlangTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ActionlangMapperTest {

    private ActionlangMapper actionlangMapper;

    @BeforeEach
    void setUp() {
        actionlangMapper = new ActionlangMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getActionlangSample1();
        var actual = actionlangMapper.toEntity(actionlangMapper.toDto(expected));
        assertActionlangAllPropertiesEquals(expected, actual);
    }
}
