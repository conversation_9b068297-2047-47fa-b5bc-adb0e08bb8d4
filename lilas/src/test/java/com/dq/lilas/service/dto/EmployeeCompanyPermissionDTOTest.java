package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeCompanyPermissionDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeCompanyPermissionDTO.class);
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO1 = new EmployeeCompanyPermissionDTO();
        employeeCompanyPermissionDTO1.setId(1L);
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO2 = new EmployeeCompanyPermissionDTO();
        assertThat(employeeCompanyPermissionDTO1).isNotEqualTo(employeeCompanyPermissionDTO2);
        employeeCompanyPermissionDTO2.setId(employeeCompanyPermissionDTO1.getId());
        assertThat(employeeCompanyPermissionDTO1).isEqualTo(employeeCompanyPermissionDTO2);
        employeeCompanyPermissionDTO2.setId(2L);
        assertThat(employeeCompanyPermissionDTO1).isNotEqualTo(employeeCompanyPermissionDTO2);
        employeeCompanyPermissionDTO1.setId(null);
        assertThat(employeeCompanyPermissionDTO1).isNotEqualTo(employeeCompanyPermissionDTO2);
    }
}
