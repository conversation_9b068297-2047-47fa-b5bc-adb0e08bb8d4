package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.GmsBrandsAsserts.*;
import static com.dq.lilas.domain.GmsBrandsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class GmsBrandsMapperTest {

    private GmsBrandsMapper gmsBrandsMapper;

    @BeforeEach
    void setUp() {
        gmsBrandsMapper = new GmsBrandsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getGmsBrandsSample1();
        var actual = gmsBrandsMapper.toEntity(gmsBrandsMapper.toDto(expected));
        assertGmsBrandsAllPropertiesEquals(expected, actual);
    }
}
