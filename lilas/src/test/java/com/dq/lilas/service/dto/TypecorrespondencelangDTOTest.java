package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TypecorrespondencelangDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TypecorrespondencelangDTO.class);
        TypecorrespondencelangDTO typecorrespondencelangDTO1 = new TypecorrespondencelangDTO();
        typecorrespondencelangDTO1.setId(1L);
        TypecorrespondencelangDTO typecorrespondencelangDTO2 = new TypecorrespondencelangDTO();
        assertThat(typecorrespondencelangDTO1).isNotEqualTo(typecorrespondencelangDTO2);
        typecorrespondencelangDTO2.setId(typecorrespondencelangDTO1.getId());
        assertThat(typecorrespondencelangDTO1).isEqualTo(typecorrespondencelangDTO2);
        typecorrespondencelangDTO2.setId(2L);
        assertThat(typecorrespondencelangDTO1).isNotEqualTo(typecorrespondencelangDTO2);
        typecorrespondencelangDTO1.setId(null);
        assertThat(typecorrespondencelangDTO1).isNotEqualTo(typecorrespondencelangDTO2);
    }
}
