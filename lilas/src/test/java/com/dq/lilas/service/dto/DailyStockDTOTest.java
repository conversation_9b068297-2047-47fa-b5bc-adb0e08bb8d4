package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DailyStockDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(DailyStockDTO.class);
        DailyStockDTO dailyStockDTO1 = new DailyStockDTO();
        dailyStockDTO1.setId(1L);
        DailyStockDTO dailyStockDTO2 = new DailyStockDTO();
        assertThat(dailyStockDTO1).isNotEqualTo(dailyStockDTO2);
        dailyStockDTO2.setId(dailyStockDTO1.getId());
        assertThat(dailyStockDTO1).isEqualTo(dailyStockDTO2);
        dailyStockDTO2.setId(2L);
        assertThat(dailyStockDTO1).isNotEqualTo(dailyStockDTO2);
        dailyStockDTO1.setId(null);
        assertThat(dailyStockDTO1).isNotEqualTo(dailyStockDTO2);
    }
}
