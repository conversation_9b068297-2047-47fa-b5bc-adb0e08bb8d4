package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UnitlangDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(UnitlangDTO.class);
        UnitlangDTO unitlangDTO1 = new UnitlangDTO();
        unitlangDTO1.setId(1L);
        UnitlangDTO unitlangDTO2 = new UnitlangDTO();
        assertThat(unitlangDTO1).isNotEqualTo(unitlangDTO2);
        unitlangDTO2.setId(unitlangDTO1.getId());
        assertThat(unitlangDTO1).isEqualTo(unitlangDTO2);
        unitlangDTO2.setId(2L);
        assertThat(unitlangDTO1).isNotEqualTo(unitlangDTO2);
        unitlangDTO1.setId(null);
        assertThat(unitlangDTO1).isNotEqualTo(unitlangDTO2);
    }
}
