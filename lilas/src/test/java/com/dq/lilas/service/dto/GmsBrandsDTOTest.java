package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class GmsBrandsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(GmsBrandsDTO.class);
        GmsBrandsDTO gmsBrandsDTO1 = new GmsBrandsDTO();
        gmsBrandsDTO1.setId(1L);
        GmsBrandsDTO gmsBrandsDTO2 = new GmsBrandsDTO();
        assertThat(gmsBrandsDTO1).isNotEqualTo(gmsBrandsDTO2);
        gmsBrandsDTO2.setId(gmsBrandsDTO1.getId());
        assertThat(gmsBrandsDTO1).isEqualTo(gmsBrandsDTO2);
        gmsBrandsDTO2.setId(2L);
        assertThat(gmsBrandsDTO1).isNotEqualTo(gmsBrandsDTO2);
        gmsBrandsDTO1.setId(null);
        assertThat(gmsBrandsDTO1).isNotEqualTo(gmsBrandsDTO2);
    }
}
