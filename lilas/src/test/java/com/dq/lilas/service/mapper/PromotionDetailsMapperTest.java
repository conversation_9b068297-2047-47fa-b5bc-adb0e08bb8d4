package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.PromotionDetailsAsserts.*;
import static com.dq.lilas.domain.PromotionDetailsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PromotionDetailsMapperTest {

    private PromotionDetailsMapper promotionDetailsMapper;

    @BeforeEach
    void setUp() {
        promotionDetailsMapper = new PromotionDetailsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getPromotionDetailsSample1();
        var actual = promotionDetailsMapper.toEntity(promotionDetailsMapper.toDto(expected));
        assertPromotionDetailsAllPropertiesEquals(expected, actual);
    }
}
