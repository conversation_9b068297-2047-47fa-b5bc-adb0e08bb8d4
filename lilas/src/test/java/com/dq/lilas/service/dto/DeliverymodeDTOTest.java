package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DeliverymodeDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(DeliverymodeDTO.class);
        DeliverymodeDTO deliverymodeDTO1 = new DeliverymodeDTO();
        deliverymodeDTO1.setId(1L);
        DeliverymodeDTO deliverymodeDTO2 = new DeliverymodeDTO();
        assertThat(deliverymodeDTO1).isNotEqualTo(deliverymodeDTO2);
        deliverymodeDTO2.setId(deliverymodeDTO1.getId());
        assertThat(deliverymodeDTO1).isEqualTo(deliverymodeDTO2);
        deliverymodeDTO2.setId(2L);
        assertThat(deliverymodeDTO1).isNotEqualTo(deliverymodeDTO2);
        deliverymodeDTO1.setId(null);
        assertThat(deliverymodeDTO1).isNotEqualTo(deliverymodeDTO2);
    }
}
