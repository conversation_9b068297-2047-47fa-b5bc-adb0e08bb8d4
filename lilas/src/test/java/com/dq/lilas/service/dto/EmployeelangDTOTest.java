package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeelangDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeelangDTO.class);
        EmployeelangDTO employeelangDTO1 = new EmployeelangDTO();
        employeelangDTO1.setId(1L);
        EmployeelangDTO employeelangDTO2 = new EmployeelangDTO();
        assertThat(employeelangDTO1).isNotEqualTo(employeelangDTO2);
        employeelangDTO2.setId(employeelangDTO1.getId());
        assertThat(employeelangDTO1).isEqualTo(employeelangDTO2);
        employeelangDTO2.setId(2L);
        assertThat(employeelangDTO1).isNotEqualTo(employeelangDTO2);
        employeelangDTO1.setId(null);
        assertThat(employeelangDTO1).isNotEqualTo(employeelangDTO2);
    }
}
