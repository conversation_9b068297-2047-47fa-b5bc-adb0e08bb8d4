package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TypecorrespondenceDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TypecorrespondenceDTO.class);
        TypecorrespondenceDTO typecorrespondenceDTO1 = new TypecorrespondenceDTO();
        typecorrespondenceDTO1.setId(1L);
        TypecorrespondenceDTO typecorrespondenceDTO2 = new TypecorrespondenceDTO();
        assertThat(typecorrespondenceDTO1).isNotEqualTo(typecorrespondenceDTO2);
        typecorrespondenceDTO2.setId(typecorrespondenceDTO1.getId());
        assertThat(typecorrespondenceDTO1).isEqualTo(typecorrespondenceDTO2);
        typecorrespondenceDTO2.setId(2L);
        assertThat(typecorrespondenceDTO1).isNotEqualTo(typecorrespondenceDTO2);
        typecorrespondenceDTO1.setId(null);
        assertThat(typecorrespondenceDTO1).isNotEqualTo(typecorrespondenceDTO2);
    }
}
