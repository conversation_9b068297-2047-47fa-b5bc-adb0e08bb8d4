package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class MailsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(MailsDTO.class);
        MailsDTO mailsDTO1 = new MailsDTO();
        mailsDTO1.setId(1L);
        MailsDTO mailsDTO2 = new MailsDTO();
        assertThat(mailsDTO1).isNotEqualTo(mailsDTO2);
        mailsDTO2.setId(mailsDTO1.getId());
        assertThat(mailsDTO1).isEqualTo(mailsDTO2);
        mailsDTO2.setId(2L);
        assertThat(mailsDTO1).isNotEqualTo(mailsDTO2);
        mailsDTO1.setId(null);
        assertThat(mailsDTO1).isNotEqualTo(mailsDTO2);
    }
}
