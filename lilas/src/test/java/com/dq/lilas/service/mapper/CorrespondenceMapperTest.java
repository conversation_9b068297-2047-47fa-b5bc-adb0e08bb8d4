package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.CorrespondenceAsserts.*;
import static com.dq.lilas.domain.CorrespondenceTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CorrespondenceMapperTest {

    private CorrespondenceMapper correspondenceMapper;

    @BeforeEach
    void setUp() {
        correspondenceMapper = new CorrespondenceMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getCorrespondenceSample1();
        var actual = correspondenceMapper.toEntity(correspondenceMapper.toDto(expected));
        assertCorrespondenceAllPropertiesEquals(expected, actual);
    }
}
