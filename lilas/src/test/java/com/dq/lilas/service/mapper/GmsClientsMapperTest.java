package com.dq.lilas.service.mapper;

import static com.dq.lilas.domain.GmsClientsAsserts.*;
import static com.dq.lilas.domain.GmsClientsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class GmsClientsMapperTest {

    private GmsClientsMapper gmsClientsMapper;

    @BeforeEach
    void setUp() {
        gmsClientsMapper = new GmsClientsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getGmsClientsSample1();
        var actual = gmsClientsMapper.toEntity(gmsClientsMapper.toDto(expected));
        assertGmsClientsAllPropertiesEquals(expected, actual);
    }
}
