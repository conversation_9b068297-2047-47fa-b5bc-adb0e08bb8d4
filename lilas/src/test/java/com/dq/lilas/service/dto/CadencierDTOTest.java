package com.dq.lilas.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class CadencierDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(CadencierDTO.class);
        CadencierDTO cadencierDTO1 = new CadencierDTO();
        cadencierDTO1.setId(1L);
        CadencierDTO cadencierDTO2 = new CadencierDTO();
        assertThat(cadencierDTO1).isNotEqualTo(cadencierDTO2);
        cadencierDTO2.setId(cadencierDTO1.getId());
        assertThat(cadencierDTO1).isEqualTo(cadencierDTO2);
        cadencierDTO2.setId(2L);
        assertThat(cadencierDTO1).isNotEqualTo(cadencierDTO2);
        cadencierDTO1.setId(null);
        assertThat(cadencierDTO1).isNotEqualTo(cadencierDTO2);
    }
}
