package com.dq.lilas.domain;

import static com.dq.lilas.domain.DeliverymodeTestSamples.*;
import static com.dq.lilas.domain.DeliverymodelangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DeliverymodelangTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Deliverymodelang.class);
        Deliverymodelang deliverymodelang1 = getDeliverymodelangSample1();
        Deliverymodelang deliverymodelang2 = new Deliverymodelang();
        assertThat(deliverymodelang1).isNotEqualTo(deliverymodelang2);

        deliverymodelang2.setId(deliverymodelang1.getId());
        assertThat(deliverymodelang1).isEqualTo(deliverymodelang2);

        deliverymodelang2 = getDeliverymodelangSample2();
        assertThat(deliverymodelang1).isNotEqualTo(deliverymodelang2);
    }

    @Test
    void deliverymodeTest() {
        Deliverymodelang deliverymodelang = getDeliverymodelangRandomSampleGenerator();
        Deliverymode deliverymodeBack = getDeliverymodeRandomSampleGenerator();

        deliverymodelang.setDeliverymode(deliverymodeBack);
        assertThat(deliverymodelang.getDeliverymode()).isEqualTo(deliverymodeBack);

        deliverymodelang.deliverymode(null);
        assertThat(deliverymodelang.getDeliverymode()).isNull();
    }
}
