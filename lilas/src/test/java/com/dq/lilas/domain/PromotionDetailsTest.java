package com.dq.lilas.domain;

import static com.dq.lilas.domain.PromotionDetailsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromotionDetailsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromotionDetails.class);
        PromotionDetails promotionDetails1 = getPromotionDetailsSample1();
        PromotionDetails promotionDetails2 = new PromotionDetails();
        assertThat(promotionDetails1).isNotEqualTo(promotionDetails2);

        promotionDetails2.setId(promotionDetails1.getId());
        assertThat(promotionDetails1).isEqualTo(promotionDetails2);

        promotionDetails2 = getPromotionDetailsSample2();
        assertThat(promotionDetails1).isNotEqualTo(promotionDetails2);
    }
}
