package com.dq.lilas.domain;

import static com.dq.lilas.domain.EmployeeGmsBrandsTestSamples.*;
import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.GmsBrandsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeGmsBrandsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeGmsBrands.class);
        EmployeeGmsBrands employeeGmsBrands1 = getEmployeeGmsBrandsSample1();
        EmployeeGmsBrands employeeGmsBrands2 = new EmployeeGmsBrands();
        assertThat(employeeGmsBrands1).isNotEqualTo(employeeGmsBrands2);

        employeeGmsBrands2.setId(employeeGmsBrands1.getId());
        assertThat(employeeGmsBrands1).isEqualTo(employeeGmsBrands2);

        employeeGmsBrands2 = getEmployeeGmsBrandsSample2();
        assertThat(employeeGmsBrands1).isNotEqualTo(employeeGmsBrands2);
    }

    @Test
    void employeeTest() {
        EmployeeGmsBrands employeeGmsBrands = getEmployeeGmsBrandsRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        employeeGmsBrands.setEmployee(employeeBack);
        assertThat(employeeGmsBrands.getEmployee()).isEqualTo(employeeBack);

        employeeGmsBrands.employee(null);
        assertThat(employeeGmsBrands.getEmployee()).isNull();
    }

    @Test
    void gmsBrandsTest() {
        EmployeeGmsBrands employeeGmsBrands = getEmployeeGmsBrandsRandomSampleGenerator();
        GmsBrands gmsBrandsBack = getGmsBrandsRandomSampleGenerator();

        employeeGmsBrands.setGmsBrands(gmsBrandsBack);
        assertThat(employeeGmsBrands.getGmsBrands()).isEqualTo(gmsBrandsBack);

        employeeGmsBrands.gmsBrands(null);
        assertThat(employeeGmsBrands.getGmsBrands()).isNull();
    }
}
