package com.dq.lilas.domain;

import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.EmployeelangTestSamples.*;
import static com.dq.lilas.domain.JobTestSamples.*;
import static com.dq.lilas.domain.UnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class EmployeeTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Employee.class);
        Employee employee1 = getEmployeeSample1();
        Employee employee2 = new Employee();
        assertThat(employee1).isNotEqualTo(employee2);

        employee2.setId(employee1.getId());
        assertThat(employee1).isEqualTo(employee2);

        employee2 = getEmployeeSample2();
        assertThat(employee1).isNotEqualTo(employee2);
    }

    @Test
    void employeelangsTest() {
        Employee employee = getEmployeeRandomSampleGenerator();
        Employeelang employeelangBack = getEmployeelangRandomSampleGenerator();

        employee.addEmployeelangs(employeelangBack);
        assertThat(employee.getEmployeelangs()).containsOnly(employeelangBack);
        assertThat(employeelangBack.getEmployee()).isEqualTo(employee);

        employee.removeEmployeelangs(employeelangBack);
        assertThat(employee.getEmployeelangs()).doesNotContain(employeelangBack);
        assertThat(employeelangBack.getEmployee()).isNull();

        employee.employeelangs(new HashSet<>(Set.of(employeelangBack)));
        assertThat(employee.getEmployeelangs()).containsOnly(employeelangBack);
        assertThat(employeelangBack.getEmployee()).isEqualTo(employee);

        employee.setEmployeelangs(new HashSet<>());
        assertThat(employee.getEmployeelangs()).doesNotContain(employeelangBack);
        assertThat(employeelangBack.getEmployee()).isNull();
    }

    @Test
    void jobTest() {
        Employee employee = getEmployeeRandomSampleGenerator();
        Job jobBack = getJobRandomSampleGenerator();

        employee.setJob(jobBack);
        assertThat(employee.getJob()).isEqualTo(jobBack);

        employee.job(null);
        assertThat(employee.getJob()).isNull();
    }

    @Test
    void unitTest() {
        Employee employee = getEmployeeRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        employee.setUnit(unitBack);
        assertThat(employee.getUnit()).isEqualTo(unitBack);

        employee.unit(null);
        assertThat(employee.getUnit()).isNull();
    }
}
