package com.dq.lilas.domain;

import static com.dq.lilas.domain.ProductsListTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ProductsListTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ProductsList.class);
        ProductsList productsList1 = getProductsListSample1();
        ProductsList productsList2 = new ProductsList();
        assertThat(productsList1).isNotEqualTo(productsList2);

        productsList2.setId(productsList1.getId());
        assertThat(productsList1).isEqualTo(productsList2);

        productsList2 = getProductsListSample2();
        assertThat(productsList1).isNotEqualTo(productsList2);
    }
}
