package com.dq.lilas.domain;

import static com.dq.lilas.domain.CompanyTestSamples.*;
import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.UnitTestSamples.*;
import static com.dq.lilas.domain.UnitlangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class UnitTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Unit.class);
        Unit unit1 = getUnitSample1();
        Unit unit2 = new Unit();
        assertThat(unit1).isNotEqualTo(unit2);

        unit2.setId(unit1.getId());
        assertThat(unit1).isEqualTo(unit2);

        unit2 = getUnitSample2();
        assertThat(unit1).isNotEqualTo(unit2);
    }

    @Test
    void unitlangsTest() {
        Unit unit = getUnitRandomSampleGenerator();
        Unitlang unitlangBack = getUnitlangRandomSampleGenerator();

        unit.addUnitlangs(unitlangBack);
        assertThat(unit.getUnitlangs()).containsOnly(unitlangBack);
        assertThat(unitlangBack.getUnit()).isEqualTo(unit);

        unit.removeUnitlangs(unitlangBack);
        assertThat(unit.getUnitlangs()).doesNotContain(unitlangBack);
        assertThat(unitlangBack.getUnit()).isNull();

        unit.unitlangs(new HashSet<>(Set.of(unitlangBack)));
        assertThat(unit.getUnitlangs()).containsOnly(unitlangBack);
        assertThat(unitlangBack.getUnit()).isEqualTo(unit);

        unit.setUnitlangs(new HashSet<>());
        assertThat(unit.getUnitlangs()).doesNotContain(unitlangBack);
        assertThat(unitlangBack.getUnit()).isNull();
    }

    @Test
    void employeesTest() {
        Unit unit = getUnitRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        unit.addEmployees(employeeBack);
        assertThat(unit.getEmployees()).containsOnly(employeeBack);
        assertThat(employeeBack.getUnit()).isEqualTo(unit);

        unit.removeEmployees(employeeBack);
        assertThat(unit.getEmployees()).doesNotContain(employeeBack);
        assertThat(employeeBack.getUnit()).isNull();

        unit.employees(new HashSet<>(Set.of(employeeBack)));
        assertThat(unit.getEmployees()).containsOnly(employeeBack);
        assertThat(employeeBack.getUnit()).isEqualTo(unit);

        unit.setEmployees(new HashSet<>());
        assertThat(unit.getEmployees()).doesNotContain(employeeBack);
        assertThat(employeeBack.getUnit()).isNull();
    }

    @Test
    void companyTest() {
        Unit unit = getUnitRandomSampleGenerator();
        Company companyBack = getCompanyRandomSampleGenerator();

        unit.setCompany(companyBack);
        assertThat(unit.getCompany()).isEqualTo(companyBack);

        unit.company(null);
        assertThat(unit.getCompany()).isNull();
    }
}
