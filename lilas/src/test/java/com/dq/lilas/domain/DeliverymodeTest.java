package com.dq.lilas.domain;

import static com.dq.lilas.domain.DeliverymodeTestSamples.*;
import static com.dq.lilas.domain.DeliverymodelangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class DeliverymodeTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Deliverymode.class);
        Deliverymode deliverymode1 = getDeliverymodeSample1();
        Deliverymode deliverymode2 = new Deliverymode();
        assertThat(deliverymode1).isNotEqualTo(deliverymode2);

        deliverymode2.setId(deliverymode1.getId());
        assertThat(deliverymode1).isEqualTo(deliverymode2);

        deliverymode2 = getDeliverymodeSample2();
        assertThat(deliverymode1).isNotEqualTo(deliverymode2);
    }

    @Test
    void deleverymodelangTest() {
        Deliverymode deliverymode = getDeliverymodeRandomSampleGenerator();
        Deliverymodelang deliverymodelangBack = getDeliverymodelangRandomSampleGenerator();

        deliverymode.addDeleverymodelang(deliverymodelangBack);
        assertThat(deliverymode.getDeleverymodelangs()).containsOnly(deliverymodelangBack);
        assertThat(deliverymodelangBack.getDeliverymode()).isEqualTo(deliverymode);

        deliverymode.removeDeleverymodelang(deliverymodelangBack);
        assertThat(deliverymode.getDeleverymodelangs()).doesNotContain(deliverymodelangBack);
        assertThat(deliverymodelangBack.getDeliverymode()).isNull();

        deliverymode.deleverymodelangs(new HashSet<>(Set.of(deliverymodelangBack)));
        assertThat(deliverymode.getDeleverymodelangs()).containsOnly(deliverymodelangBack);
        assertThat(deliverymodelangBack.getDeliverymode()).isEqualTo(deliverymode);

        deliverymode.setDeleverymodelangs(new HashSet<>());
        assertThat(deliverymode.getDeleverymodelangs()).doesNotContain(deliverymodelangBack);
        assertThat(deliverymodelangBack.getDeliverymode()).isNull();
    }
}
