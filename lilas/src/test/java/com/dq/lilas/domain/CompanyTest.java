package com.dq.lilas.domain;

import static com.dq.lilas.domain.CompanyTestSamples.*;
import static com.dq.lilas.domain.UnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class CompanyTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Company.class);
        Company company1 = getCompanySample1();
        Company company2 = new Company();
        assertThat(company1).isNotEqualTo(company2);

        company2.setId(company1.getId());
        assertThat(company1).isEqualTo(company2);

        company2 = getCompanySample2();
        assertThat(company1).isNotEqualTo(company2);
    }

    @Test
    void unitsTest() {
        Company company = getCompanyRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        company.addUnit(unitBack);
        assertThat(company.getUnits()).containsOnly(unitBack);
        assertThat(unitBack.getCompany()).isEqualTo(company);

        company.removeUnit(unitBack);
        assertThat(company.getUnits()).doesNotContain(unitBack);
        assertThat(unitBack.getCompany()).isNull();

        company.units(new HashSet<>(Set.of(unitBack)));
        assertThat(company.getUnits()).containsOnly(unitBack);
        assertThat(unitBack.getCompany()).isEqualTo(company);

        company.setUnits(new HashSet<>());
        assertThat(company.getUnits()).doesNotContain(unitBack);
        assertThat(unitBack.getCompany()).isNull();
    }
}
