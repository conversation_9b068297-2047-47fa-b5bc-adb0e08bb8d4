package com.dq.lilas.domain;

import static com.dq.lilas.domain.TypecorrespondenceTestSamples.*;
import static com.dq.lilas.domain.TypecorrespondencelangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TypecorrespondencelangTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Typecorrespondencelang.class);
        Typecorrespondencelang typecorrespondencelang1 = getTypecorrespondencelangSample1();
        Typecorrespondencelang typecorrespondencelang2 = new Typecorrespondencelang();
        assertThat(typecorrespondencelang1).isNotEqualTo(typecorrespondencelang2);

        typecorrespondencelang2.setId(typecorrespondencelang1.getId());
        assertThat(typecorrespondencelang1).isEqualTo(typecorrespondencelang2);

        typecorrespondencelang2 = getTypecorrespondencelangSample2();
        assertThat(typecorrespondencelang1).isNotEqualTo(typecorrespondencelang2);
    }

    @Test
    void typecorrespondenceTest() {
        Typecorrespondencelang typecorrespondencelang = getTypecorrespondencelangRandomSampleGenerator();
        Typecorrespondence typecorrespondenceBack = getTypecorrespondenceRandomSampleGenerator();

        typecorrespondencelang.setTypecorrespondence(typecorrespondenceBack);
        assertThat(typecorrespondencelang.getTypecorrespondence()).isEqualTo(typecorrespondenceBack);

        typecorrespondencelang.typecorrespondence(null);
        assertThat(typecorrespondencelang.getTypecorrespondence()).isNull();
    }
}
