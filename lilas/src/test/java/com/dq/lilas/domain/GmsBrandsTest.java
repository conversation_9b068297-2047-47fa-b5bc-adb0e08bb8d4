package com.dq.lilas.domain;

import static com.dq.lilas.domain.GmsBrandsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class GmsBrandsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(GmsBrands.class);
        GmsBrands gmsBrands1 = getGmsBrandsSample1();
        GmsBrands gmsBrands2 = new GmsBrands();
        assertThat(gmsBrands1).isNotEqualTo(gmsBrands2);

        gmsBrands2.setId(gmsBrands1.getId());
        assertThat(gmsBrands1).isEqualTo(gmsBrands2);

        gmsBrands2 = getGmsBrandsSample2();
        assertThat(gmsBrands1).isNotEqualTo(gmsBrands2);
    }
}
