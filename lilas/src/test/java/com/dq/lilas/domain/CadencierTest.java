package com.dq.lilas.domain;

import static com.dq.lilas.domain.CadencierTestSamples.*;
import static com.dq.lilas.domain.GmsClientsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class CadencierTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Cadencier.class);
        Cadencier cadencier1 = getCadencierSample1();
        Cadencier cadencier2 = new Cadencier();
        assertThat(cadencier1).isNotEqualTo(cadencier2);

        cadencier2.setId(cadencier1.getId());
        assertThat(cadencier1).isEqualTo(cadencier2);

        cadencier2 = getCadencierSample2();
        assertThat(cadencier1).isNotEqualTo(cadencier2);
    }

    @Test
    void gmsClientsTest() {
        Cadencier cadencier = getCadencierRandomSampleGenerator();
        GmsClients gmsClientsBack = getGmsClientsRandomSampleGenerator();

        cadencier.setGmsClients(gmsClientsBack);
        assertThat(cadencier.getGmsClients()).isEqualTo(gmsClientsBack);
        // Note: GmsClients entity no longer has a cadencier field, so bidirectional check removed

        cadencier.gmsClients(null);
        assertThat(cadencier.getGmsClients()).isNull();
        // Note: GmsClients entity no longer has a cadencier field, so bidirectional check removed
    }
}
