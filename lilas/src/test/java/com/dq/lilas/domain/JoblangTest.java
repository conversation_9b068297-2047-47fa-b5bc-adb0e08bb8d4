package com.dq.lilas.domain;

import static com.dq.lilas.domain.JobTestSamples.*;
import static com.dq.lilas.domain.JoblangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class JoblangTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Joblang.class);
        Joblang joblang1 = getJoblangSample1();
        Joblang joblang2 = new Joblang();
        assertThat(joblang1).isNotEqualTo(joblang2);

        joblang2.setId(joblang1.getId());
        assertThat(joblang1).isEqualTo(joblang2);

        joblang2 = getJoblangSample2();
        assertThat(joblang1).isNotEqualTo(joblang2);
    }

    @Test
    void jobTest() {
        Joblang joblang = getJoblangRandomSampleGenerator();
        Job jobBack = getJobRandomSampleGenerator();

        joblang.setJob(jobBack);
        assertThat(joblang.getJob()).isEqualTo(jobBack);

        joblang.job(null);
        assertThat(joblang.getJob()).isNull();
    }
}
