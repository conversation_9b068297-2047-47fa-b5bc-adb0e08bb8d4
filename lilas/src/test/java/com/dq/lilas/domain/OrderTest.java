package com.dq.lilas.domain;

import static com.dq.lilas.domain.DailyBatchesTestSamples.*;
import static com.dq.lilas.domain.GmsClientsTestSamples.*;
import static com.dq.lilas.domain.MailsTestSamples.*;
import static com.dq.lilas.domain.OrderDetailsTestSamples.*;
import static com.dq.lilas.domain.OrderTestSamples.*;
import static com.dq.lilas.domain.TemplateConditionsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class OrderTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Order.class);
        Order order1 = getOrderSample1();
        Order order2 = new Order();
        assertThat(order1).isNotEqualTo(order2);

        order2.setId(order1.getId());
        assertThat(order1).isEqualTo(order2);

        order2 = getOrderSample2();
        assertThat(order1).isNotEqualTo(order2);
    }

    @Test
    void orderDetailsTest() {
        Order order = getOrderRandomSampleGenerator();
        OrderDetails orderDetailsBack = getOrderDetailsRandomSampleGenerator();

        order.addOrderDetails(orderDetailsBack);
        assertThat(order.getOrderDetails()).containsOnly(orderDetailsBack);
        assertThat(orderDetailsBack.getOrder()).isEqualTo(order);

        order.removeOrderDetails(orderDetailsBack);
        assertThat(order.getOrderDetails()).doesNotContain(orderDetailsBack);
        assertThat(orderDetailsBack.getOrder()).isNull();

        order.orderDetails(new HashSet<>(Set.of(orderDetailsBack)));
        assertThat(order.getOrderDetails()).containsOnly(orderDetailsBack);
        assertThat(orderDetailsBack.getOrder()).isEqualTo(order);

        order.setOrderDetails(new HashSet<>());
        assertThat(order.getOrderDetails()).doesNotContain(orderDetailsBack);
        assertThat(orderDetailsBack.getOrder()).isNull();
    }

    @Test
    void dailyBatchesTest() {
        Order order = getOrderRandomSampleGenerator();
        DailyBatches dailyBatchesBack = getDailyBatchesRandomSampleGenerator();

        order.setDailyBatches(dailyBatchesBack);
        assertThat(order.getDailyBatches()).isEqualTo(dailyBatchesBack);

        order.dailyBatches(null);
        assertThat(order.getDailyBatches()).isNull();
    }

    @Test
    void gmsClientsTest() {
        Order order = getOrderRandomSampleGenerator();
        GmsClients gmsClientsBack = getGmsClientsRandomSampleGenerator();

        order.setGmsClients(gmsClientsBack);
        assertThat(order.getGmsClients()).isEqualTo(gmsClientsBack);

        order.gmsClients(null);
        assertThat(order.getGmsClients()).isNull();
    }

    @Test
    void templateConditionsTest() {
        Order order = getOrderRandomSampleGenerator();
        TemplateConditions templateConditionsBack = getTemplateConditionsRandomSampleGenerator();

        order.setTemplateConditions(templateConditionsBack);
        assertThat(order.getTemplateConditions()).isEqualTo(templateConditionsBack);

        order.templateConditions(null);
        assertThat(order.getTemplateConditions()).isNull();
    }

    @Test
    void mailsTest() {
        Order order = getOrderRandomSampleGenerator();
        Mails mailsBack = getMailsRandomSampleGenerator();

        order.setMails(mailsBack);
        assertThat(order.getMails()).isEqualTo(mailsBack);

        order.mails(null);
        assertThat(order.getMails()).isNull();
    }
}
