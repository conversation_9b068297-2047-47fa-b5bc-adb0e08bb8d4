package com.dq.lilas.domain;

import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.EmployeelangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeelangTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Employeelang.class);
        Employeelang employeelang1 = getEmployeelangSample1();
        Employeelang employeelang2 = new Employeelang();
        assertThat(employeelang1).isNotEqualTo(employeelang2);

        employeelang2.setId(employeelang1.getId());
        assertThat(employeelang1).isEqualTo(employeelang2);

        employeelang2 = getEmployeelangSample2();
        assertThat(employeelang1).isNotEqualTo(employeelang2);
    }

    @Test
    void employeeTest() {
        Employeelang employeelang = getEmployeelangRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        employeelang.setEmployee(employeeBack);
        assertThat(employeelang.getEmployee()).isEqualTo(employeeBack);

        employeelang.employee(null);
        assertThat(employeelang.getEmployee()).isNull();
    }
}
