package com.dq.lilas.domain;

import static com.dq.lilas.domain.MailsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class MailsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Mails.class);
        Mails mails1 = getMailsSample1();
        Mails mails2 = new Mails();
        assertThat(mails1).isNotEqualTo(mails2);

        mails2.setId(mails1.getId());
        assertThat(mails1).isEqualTo(mails2);

        mails2 = getMailsSample2();
        assertThat(mails1).isNotEqualTo(mails2);
    }
}
