package com.dq.lilas.domain;

import static com.dq.lilas.domain.CompanyTestSamples.*;
import static com.dq.lilas.domain.EmployeeCompanyPermissionTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeCompanyPermissionTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeCompanyPermission.class);
        EmployeeCompanyPermission employeeCompanyPermission1 = getEmployeeCompanyPermissionSample1();
        EmployeeCompanyPermission employeeCompanyPermission2 = new EmployeeCompanyPermission();
        assertThat(employeeCompanyPermission1).isNotEqualTo(employeeCompanyPermission2);

        employeeCompanyPermission2.setId(employeeCompanyPermission1.getId());
        assertThat(employeeCompanyPermission1).isEqualTo(employeeCompanyPermission2);

        employeeCompanyPermission2 = getEmployeeCompanyPermissionSample2();
        assertThat(employeeCompanyPermission1).isNotEqualTo(employeeCompanyPermission2);
    }

    @Test
    void companyTest() {
        EmployeeCompanyPermission employeeCompanyPermission = getEmployeeCompanyPermissionRandomSampleGenerator();
        Company companyBack = getCompanyRandomSampleGenerator();

        employeeCompanyPermission.setCompany(companyBack);
        assertThat(employeeCompanyPermission.getCompany()).isEqualTo(companyBack);

        employeeCompanyPermission.company(null);
        assertThat(employeeCompanyPermission.getCompany()).isNull();
    }
}
