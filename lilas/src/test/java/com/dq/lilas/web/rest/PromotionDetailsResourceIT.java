package com.dq.lilas.web.rest;

import static com.dq.lilas.domain.PromotionDetailsAsserts.*;
import static com.dq.lilas.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.PromotionDetails;
import com.dq.lilas.repository.PromotionDetailsRepository;
import com.dq.lilas.service.dto.PromotionDetailsDTO;
import com.dq.lilas.service.mapper.PromotionDetailsMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PromotionDetailsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class PromotionDetailsResourceIT {

    private static final String DEFAULT_CODE_PRODUIT = "AAAAAAAAAA";
    private static final String UPDATED_CODE_PRODUIT = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final Double DEFAULT_PRICE_HT = 1D;
    private static final Double UPDATED_PRICE_HT = 2D;

    private static final Double DEFAULT_REMISE_FIXE = 1D;
    private static final Double UPDATED_REMISE_FIXE = 2D;

    private static final Double DEFAULT_REMISE_DE_PRO = 1D;
    private static final Double UPDATED_REMISE_DE_PRO = 2D;

    private static final Double DEFAULT_GRATUIT_EN_NAT = 1D;
    private static final Double UPDATED_GRATUIT_EN_NAT = 2D;

    private static final String DEFAULT_APPRO_MANAGER_DU_MAGASIN = "AAAAAAAAAA";
    private static final String UPDATED_APPRO_MANAGER_DU_MAGASIN = "BBBBBBBBBB";

    private static final String DEFAULT_APPRO_MANAGER_GMS = "AAAAAAAAAA";
    private static final String UPDATED_APPRO_MANAGER_GMS = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/promotion-details";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private PromotionDetailsRepository promotionDetailsRepository;

    @Autowired
    private PromotionDetailsMapper promotionDetailsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPromotionDetailsMockMvc;

    private PromotionDetails promotionDetails;

    private PromotionDetails insertedPromotionDetails;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromotionDetails createEntity() {
        return new PromotionDetails()
            .codeProduit(DEFAULT_CODE_PRODUIT)
            .description(DEFAULT_DESCRIPTION)
            .priceHT(DEFAULT_PRICE_HT)
            .remiseFixe(DEFAULT_REMISE_FIXE)
            .remiseDePro(DEFAULT_REMISE_DE_PRO)
            .gratuitEnNat(DEFAULT_GRATUIT_EN_NAT)
            .approManagerDuMagasin(DEFAULT_APPRO_MANAGER_DU_MAGASIN)
            .approManagerGMS(DEFAULT_APPRO_MANAGER_GMS);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromotionDetails createUpdatedEntity() {
        return new PromotionDetails()
            .codeProduit(UPDATED_CODE_PRODUIT)
            .description(UPDATED_DESCRIPTION)
            .priceHT(UPDATED_PRICE_HT)
            .remiseFixe(UPDATED_REMISE_FIXE)
            .remiseDePro(UPDATED_REMISE_DE_PRO)
            .gratuitEnNat(UPDATED_GRATUIT_EN_NAT)
            .approManagerDuMagasin(UPDATED_APPRO_MANAGER_DU_MAGASIN)
            .approManagerGMS(UPDATED_APPRO_MANAGER_GMS);
    }

    @BeforeEach
    void initTest() {
        promotionDetails = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedPromotionDetails != null) {
            promotionDetailsRepository.delete(insertedPromotionDetails);
            insertedPromotionDetails = null;
        }
    }

    @Test
    @Transactional
    void createPromotionDetails() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);
        var returnedPromotionDetailsDTO = om.readValue(
            restPromotionDetailsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promotionDetailsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            PromotionDetailsDTO.class
        );

        // Validate the PromotionDetails in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedPromotionDetails = promotionDetailsMapper.toEntity(returnedPromotionDetailsDTO);
        assertPromotionDetailsUpdatableFieldsEquals(returnedPromotionDetails, getPersistedPromotionDetails(returnedPromotionDetails));

        insertedPromotionDetails = returnedPromotionDetails;
    }

    @Test
    @Transactional
    void createPromotionDetailsWithExistingId() throws Exception {
        // Create the PromotionDetails with an existing ID
        promotionDetails.setId(1L);
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restPromotionDetailsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promotionDetailsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllPromotionDetails() throws Exception {
        // Initialize the database
        insertedPromotionDetails = promotionDetailsRepository.saveAndFlush(promotionDetails);

        // Get all the promotionDetailsList
        restPromotionDetailsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(promotionDetails.getId().intValue())))
            .andExpect(jsonPath("$.[*].codeProduit").value(hasItem(DEFAULT_CODE_PRODUIT)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].priceHT").value(hasItem(DEFAULT_PRICE_HT)))
            .andExpect(jsonPath("$.[*].remiseFixe").value(hasItem(DEFAULT_REMISE_FIXE)))
            .andExpect(jsonPath("$.[*].remiseDePro").value(hasItem(DEFAULT_REMISE_DE_PRO)))
            .andExpect(jsonPath("$.[*].gratuitEnNat").value(hasItem(DEFAULT_GRATUIT_EN_NAT)))
            .andExpect(jsonPath("$.[*].approManagerDuMagasin").value(hasItem(DEFAULT_APPRO_MANAGER_DU_MAGASIN)))
            .andExpect(jsonPath("$.[*].approManagerGMS").value(hasItem(DEFAULT_APPRO_MANAGER_GMS)));
    }

    @Test
    @Transactional
    void getPromotionDetails() throws Exception {
        // Initialize the database
        insertedPromotionDetails = promotionDetailsRepository.saveAndFlush(promotionDetails);

        // Get the promotionDetails
        restPromotionDetailsMockMvc
            .perform(get(ENTITY_API_URL_ID, promotionDetails.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(promotionDetails.getId().intValue()))
            .andExpect(jsonPath("$.codeProduit").value(DEFAULT_CODE_PRODUIT))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.priceHT").value(DEFAULT_PRICE_HT))
            .andExpect(jsonPath("$.remiseFixe").value(DEFAULT_REMISE_FIXE))
            .andExpect(jsonPath("$.remiseDePro").value(DEFAULT_REMISE_DE_PRO))
            .andExpect(jsonPath("$.gratuitEnNat").value(DEFAULT_GRATUIT_EN_NAT))
            .andExpect(jsonPath("$.approManagerDuMagasin").value(DEFAULT_APPRO_MANAGER_DU_MAGASIN))
            .andExpect(jsonPath("$.approManagerGMS").value(DEFAULT_APPRO_MANAGER_GMS));
    }

    @Test
    @Transactional
    void getNonExistingPromotionDetails() throws Exception {
        // Get the promotionDetails
        restPromotionDetailsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPromotionDetails() throws Exception {
        // Initialize the database
        insertedPromotionDetails = promotionDetailsRepository.saveAndFlush(promotionDetails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promotionDetails
        PromotionDetails updatedPromotionDetails = promotionDetailsRepository.findById(promotionDetails.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedPromotionDetails are not directly saved in db
        em.detach(updatedPromotionDetails);
        updatedPromotionDetails
            .codeProduit(UPDATED_CODE_PRODUIT)
            .description(UPDATED_DESCRIPTION)
            .priceHT(UPDATED_PRICE_HT)
            .remiseFixe(UPDATED_REMISE_FIXE)
            .remiseDePro(UPDATED_REMISE_DE_PRO)
            .gratuitEnNat(UPDATED_GRATUIT_EN_NAT)
            .approManagerDuMagasin(UPDATED_APPRO_MANAGER_DU_MAGASIN)
            .approManagerGMS(UPDATED_APPRO_MANAGER_GMS);
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(updatedPromotionDetails);

        restPromotionDetailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promotionDetailsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promotionDetailsDTO))
            )
            .andExpect(status().isOk());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedPromotionDetailsToMatchAllProperties(updatedPromotionDetails);
    }

    @Test
    @Transactional
    void putNonExistingPromotionDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promotionDetails.setId(longCount.incrementAndGet());

        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromotionDetailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promotionDetailsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promotionDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPromotionDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promotionDetails.setId(longCount.incrementAndGet());

        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromotionDetailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promotionDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPromotionDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promotionDetails.setId(longCount.incrementAndGet());

        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromotionDetailsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promotionDetailsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePromotionDetailsWithPatch() throws Exception {
        // Initialize the database
        insertedPromotionDetails = promotionDetailsRepository.saveAndFlush(promotionDetails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promotionDetails using partial update
        PromotionDetails partialUpdatedPromotionDetails = new PromotionDetails();
        partialUpdatedPromotionDetails.setId(promotionDetails.getId());

        partialUpdatedPromotionDetails
            .codeProduit(UPDATED_CODE_PRODUIT)
            .remiseFixe(UPDATED_REMISE_FIXE)
            .remiseDePro(UPDATED_REMISE_DE_PRO)
            .approManagerDuMagasin(UPDATED_APPRO_MANAGER_DU_MAGASIN)
            .approManagerGMS(UPDATED_APPRO_MANAGER_GMS);

        restPromotionDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromotionDetails.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromotionDetails))
            )
            .andExpect(status().isOk());

        // Validate the PromotionDetails in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromotionDetailsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedPromotionDetails, promotionDetails),
            getPersistedPromotionDetails(promotionDetails)
        );
    }

    @Test
    @Transactional
    void fullUpdatePromotionDetailsWithPatch() throws Exception {
        // Initialize the database
        insertedPromotionDetails = promotionDetailsRepository.saveAndFlush(promotionDetails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promotionDetails using partial update
        PromotionDetails partialUpdatedPromotionDetails = new PromotionDetails();
        partialUpdatedPromotionDetails.setId(promotionDetails.getId());

        partialUpdatedPromotionDetails
            .codeProduit(UPDATED_CODE_PRODUIT)
            .description(UPDATED_DESCRIPTION)
            .priceHT(UPDATED_PRICE_HT)
            .remiseFixe(UPDATED_REMISE_FIXE)
            .remiseDePro(UPDATED_REMISE_DE_PRO)
            .gratuitEnNat(UPDATED_GRATUIT_EN_NAT)
            .approManagerDuMagasin(UPDATED_APPRO_MANAGER_DU_MAGASIN)
            .approManagerGMS(UPDATED_APPRO_MANAGER_GMS);

        restPromotionDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromotionDetails.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromotionDetails))
            )
            .andExpect(status().isOk());

        // Validate the PromotionDetails in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromotionDetailsUpdatableFieldsEquals(
            partialUpdatedPromotionDetails,
            getPersistedPromotionDetails(partialUpdatedPromotionDetails)
        );
    }

    @Test
    @Transactional
    void patchNonExistingPromotionDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promotionDetails.setId(longCount.incrementAndGet());

        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromotionDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, promotionDetailsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promotionDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPromotionDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promotionDetails.setId(longCount.incrementAndGet());

        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromotionDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promotionDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPromotionDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promotionDetails.setId(longCount.incrementAndGet());

        // Create the PromotionDetails
        PromotionDetailsDTO promotionDetailsDTO = promotionDetailsMapper.toDto(promotionDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromotionDetailsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(promotionDetailsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromotionDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePromotionDetails() throws Exception {
        // Initialize the database
        insertedPromotionDetails = promotionDetailsRepository.saveAndFlush(promotionDetails);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the promotionDetails
        restPromotionDetailsMockMvc
            .perform(delete(ENTITY_API_URL_ID, promotionDetails.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return promotionDetailsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected PromotionDetails getPersistedPromotionDetails(PromotionDetails promotionDetails) {
        return promotionDetailsRepository.findById(promotionDetails.getId()).orElseThrow();
    }

    protected void assertPersistedPromotionDetailsToMatchAllProperties(PromotionDetails expectedPromotionDetails) {
        assertPromotionDetailsAllPropertiesEquals(expectedPromotionDetails, getPersistedPromotionDetails(expectedPromotionDetails));
    }

    protected void assertPersistedPromotionDetailsToMatchUpdatableProperties(PromotionDetails expectedPromotionDetails) {
        assertPromotionDetailsAllUpdatablePropertiesEquals(
            expectedPromotionDetails,
            getPersistedPromotionDetails(expectedPromotionDetails)
        );
    }
}
