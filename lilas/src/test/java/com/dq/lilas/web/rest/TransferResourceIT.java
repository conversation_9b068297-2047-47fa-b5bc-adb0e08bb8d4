package com.dq.lilas.web.rest;

import static com.dq.lilas.domain.TransferAsserts.*;
import static com.dq.lilas.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Transfer;
import com.dq.lilas.repository.TransferRepository;
import com.dq.lilas.service.dto.TransferDTO;
import com.dq.lilas.service.mapper.TransferMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TransferResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TransferResourceIT {

    private static final String DEFAULT_DOCYEAR = "AAAAAAAAAA";
    private static final String UPDATED_DOCYEAR = "BBBBBBBBBB";

    private static final String DEFAULT_TEXTTRANSFER = "AAAAAAAAAA";
    private static final String UPDATED_TEXTTRANSFER = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCTRANSFER = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCTRANSFER = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRTRANSFER = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRTRANSFER = "BBBBBBBBBB";

    private static final String DEFAULT_STATUSTRANSFER = "AAAAA";
    private static final String UPDATED_STATUSTRANSFER = "BBBBB";

    private static final Instant DEFAULT_DATESENDJCTRANSFER = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATESENDJCTRANSFER = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATESENDHJRTRANSFER = "AAAAAAAAAA";
    private static final String UPDATED_DATESENDHJRTRANSFER = "BBBBBBBBBB";

    private static final String DEFAULT_SAVETRANSFER = "AAAAA";
    private static final String UPDATED_SAVETRANSFER = "BBBBB";

    private static final String DEFAULT_NUMCOPY = "AAAAAAAAAA";
    private static final String UPDATED_NUMCOPY = "BBBBBBBBBB";

    private static final String DEFAULT_HIGHLEVEL = "A";
    private static final String UPDATED_HIGHLEVEL = "B";

    private static final String DEFAULT_CONFIDENTIEL = "AAAAAAAAAA";
    private static final String UPDATED_CONFIDENTIEL = "BBBBBBBBBB";

    private static final String DEFAULT_PRIORITY = "A";
    private static final String UPDATED_PRIORITY = "B";

    private static final String DEFAULT_TIMEACTION = "AAAAAAAAAA";
    private static final String UPDATED_TIMEACTION = "BBBBBBBBBB";

    private static final Instant DEFAULT_DEADLINE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DEADLINE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_RAPPELNUM = "AAAAAAAAAA";
    private static final String UPDATED_RAPPELNUM = "BBBBBBBBBB";

    private static final String DEFAULT_RAPPELTYPE = "AAAAAAAAAA";
    private static final String UPDATED_RAPPELTYPE = "BBBBBBBBBB";

    private static final String DEFAULT_READREQUEST = "A";
    private static final String UPDATED_READREQUEST = "B";

    private static final String DEFAULT_TYPERECEIVE = "AAAAAAAAAA";
    private static final String UPDATED_TYPERECEIVE = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCRECEIVE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCRECEIVE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRRECEIVE = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRRECEIVE = "BBBBBBBBBB";

    private static final String DEFAULT_HEUREARCH = "AAAAAAAAAA";
    private static final String UPDATED_HEUREARCH = "BBBBBBBBBB";

    private static final String DEFAULT_ACTIONTYPE = "AAAAAAAAAA";
    private static final String UPDATED_ACTIONTYPE = "BBBBBBBBBB";

    private static final String DEFAULT_COMMENTS = "AAAAAAAAAA";
    private static final String UPDATED_COMMENTS = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEARCH = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEARCH = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEARCHHJ = "AAAAAAAAAA";
    private static final String UPDATED_DATEARCHHJ = "BBBBBBBBBB";

    private static final String DEFAULT_LASTTRANSSERIAL = "AAAAAAAAAA";
    private static final String UPDATED_LASTTRANSSERIAL = "BBBBBBBBBB";

    private static final String DEFAULT_ADRSBOOKTRANSTO = "AAAAAAAAAA";
    private static final String UPDATED_ADRSBOOKTRANSTO = "BBBBBBBBBB";

    private static final String DEFAULT_STATUSRECEIVETO = "AAAAAAAAAA";
    private static final String UPDATED_STATUSRECEIVETO = "BBBBBBBBBB";

    private static final String DEFAULT_COMMENTSRECEIVETO = "AAAAAAAAAA";
    private static final String UPDATED_COMMENTSRECEIVETO = "BBBBBBBBBB";

    private static final Instant DEFAULT_RECEIVEDATEJCUSERTO = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_RECEIVEDATEJCUSERTO = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_RECEIVEDATEHJRUSERTO = "AAAAAAAAAA";
    private static final String UPDATED_RECEIVEDATEHJRUSERTO = "BBBBBBBBBB";

    private static final String DEFAULT_TYPETRANSFER = "AAAAAAAAAA";
    private static final String UPDATED_TYPETRANSFER = "BBBBBBBBBB";

    private static final String DEFAULT_TRANSRECSERIAL = "AAAAAAAAAA";
    private static final String UPDATED_TRANSRECSERIAL = "BBBBBBBBBB";

    private static final String DEFAULT_ATTACH = "AAAAAAAAAA";
    private static final String UPDATED_ATTACH = "BBBBBBBBBB";

    private static final String DEFAULT_TRANSTYPE = "AAAAAAAAAA";
    private static final String UPDATED_TRANSTYPE = "BBBBBBBBBB";

    private static final String DEFAULT_ORDERNBR = "AAAAAAAAAA";
    private static final String UPDATED_ORDERNBR = "BBBBBBBBBB";

    private static final String DEFAULT_HEUREACTION = "AAAAAAAAAA";
    private static final String UPDATED_HEUREACTION = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCACTION = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCACTION = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRACTION = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRACTION = "BBBBBBBBBB";

    private static final String DEFAULT_STATUSDENIED = "AAAAAAAAAA";
    private static final String UPDATED_STATUSDENIED = "BBBBBBBBBB";

    private static final String DEFAULT_SUBJECTCORRESP = "AAAAAAAAAA";
    private static final String UPDATED_SUBJECTCORRESP = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCCORRESP = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCCORRESP = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRCORRESP = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRCORRESP = "BBBBBBBBBB";

    private static final String DEFAULT_OLDSTATUS = "AAAAAAAAAA";
    private static final String UPDATED_OLDSTATUS = "BBBBBBBBBB";

    private static final String DEFAULT_STEP = "AAAAAAAAAA";
    private static final String UPDATED_STEP = "BBBBBBBBBB";

    private static final String DEFAULT_TYPEPROCESS = "AAAAAAAAAA";
    private static final String UPDATED_TYPEPROCESS = "BBBBBBBBBB";

    private static final String DEFAULT_CODETASK = "AAAAAAAAAA";
    private static final String UPDATED_CODETASK = "BBBBBBBBBB";

    private static final String DEFAULT_REFUSETEXT = "AAAAAAAAAA";
    private static final String UPDATED_REFUSETEXT = "BBBBBBBBBB";

    private static final String DEFAULT_STATUSREFUSED = "AAAAAAAAAA";
    private static final String UPDATED_STATUSREFUSED = "BBBBBBBBBB";

    private static final String DEFAULT_BIDADRSBOOK = "AAAAAAAAAA";
    private static final String UPDATED_BIDADRSBOOK = "BBBBBBBBBB";

    private static final String DEFAULT_PAGENBRPAPER = "AAAAAAAAAA";
    private static final String UPDATED_PAGENBRPAPER = "BBBBBBBBBB";

    private static final String DEFAULT_FLAGPRINT = "AAAAAAAAAA";
    private static final String UPDATED_FLAGPRINT = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEPRINT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEPRINT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRPRINT = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRPRINT = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCDELETE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCDELETE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_DATEJCREVOKE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCREVOKE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRREVOKE = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRREVOKE = "BBBBBBBBBB";

    private static final String DEFAULT_GABARITCONTEXT = "AAAAAAAAAA";
    private static final String UPDATED_GABARITCONTEXT = "BBBBBBBBBB";

    private static final String DEFAULT_APPROVEDSPEECH = "AAAAAAAAAA";
    private static final String UPDATED_APPROVEDSPEECH = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCAPPROVEDSPEECH = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCAPPROVEDSPEECH = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRAPPROVEDSPEECH = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRAPPROVEDSPEECH = "BBBBBBBBBB";

    private static final String DEFAULT_CONFORMITYTASK = "AAAAAAAAAA";
    private static final String UPDATED_CONFORMITYTASK = "BBBBBBBBBB";

    private static final String DEFAULT_USERADRSBOOK = "AAAAAAAAAA";
    private static final String UPDATED_USERADRSBOOK = "BBBBBBBBBB";

    private static final String DEFAULT_STEPMAXWF = "AAAAAAAAAA";
    private static final String UPDATED_STEPMAXWF = "BBBBBBBBBB";

    private static final String DEFAULT_INCIDENTTRANSFER = "AAAAA";
    private static final String UPDATED_INCIDENTTRANSFER = "BBBBB";

    private static final String DEFAULT_QUALIFICATIONINCIDENT = "AAAAAAAAAA";
    private static final String UPDATED_QUALIFICATIONINCIDENT = "BBBBBBBBBB";

    private static final String DEFAULT_CATEGORIEINCIDENT = "AAAAAAAAAA";
    private static final String UPDATED_CATEGORIEINCIDENT = "BBBBBBBBBB";

    private static final String DEFAULT_STATUTINCIDENT = "AAAAAAAAAA";
    private static final String UPDATED_STATUTINCIDENT = "BBBBBBBBBB";

    private static final String DEFAULT_CRITICITEINCIDENT = "AAAAAAAAAA";
    private static final String UPDATED_CRITICITEINCIDENT = "BBBBBBBBBB";

    private static final String DEFAULT_VOICE_ID = "AAAAAAAAAA";
    private static final String UPDATED_VOICE_ID = "BBBBBBBBBB";

    private static final String DEFAULT_FAVORIS = "A";
    private static final String UPDATED_FAVORIS = "B";

    private static final String DEFAULT_CHECKINBOXFAVORITE = "A";
    private static final String UPDATED_CHECKINBOXFAVORITE = "B";

    private static final String DEFAULT_CHECKCLOSEFAVORITE = "A";
    private static final String UPDATED_CHECKCLOSEFAVORITE = "B";

    private static final String DEFAULT_CHECKFAVORITE = "A";
    private static final String UPDATED_CHECKFAVORITE = "B";

    private static final String DEFAULT_TASKCATEG_ID = "AAAAAAAAAA";
    private static final String UPDATED_TASKCATEG_ID = "BBBBBBBBBB";

    private static final Boolean DEFAULT_PINREQUIRED = false;
    private static final Boolean UPDATED_PINREQUIRED = true;

    private static final String DEFAULT_CODE_PIN = "AAAAAA";
    private static final String UPDATED_CODE_PIN = "BBBBBB";

    private static final Boolean DEFAULT_SENDWITHMAIL = false;
    private static final Boolean UPDATED_SENDWITHMAIL = true;

    private static final String ENTITY_API_URL = "/api/transfers";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TransferRepository transferRepository;

    @Autowired
    private TransferMapper transferMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTransferMockMvc;

    private Transfer transfer;

    private Transfer insertedTransfer;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Transfer createEntity() {
        return new Transfer()
            .docyear(DEFAULT_DOCYEAR)
            .texttransfer(DEFAULT_TEXTTRANSFER)
            .datejctransfer(DEFAULT_DATEJCTRANSFER)
            .datehjrtransfer(DEFAULT_DATEHJRTRANSFER)
            .statustransfer(DEFAULT_STATUSTRANSFER)
            .datesendjctransfer(DEFAULT_DATESENDJCTRANSFER)
            .datesendhjrtransfer(DEFAULT_DATESENDHJRTRANSFER)
            .savetransfer(DEFAULT_SAVETRANSFER)
            .numcopy(DEFAULT_NUMCOPY)
            .highlevel(DEFAULT_HIGHLEVEL)
            .confidentiel(DEFAULT_CONFIDENTIEL)
            .priority(DEFAULT_PRIORITY)
            .timeaction(DEFAULT_TIMEACTION)
            .deadline(DEFAULT_DEADLINE)
            .rappelnum(DEFAULT_RAPPELNUM)
            .rappeltype(DEFAULT_RAPPELTYPE)
            .readrequest(DEFAULT_READREQUEST)
            .typereceive(DEFAULT_TYPERECEIVE)
            .datejcreceive(DEFAULT_DATEJCRECEIVE)
            .datehjrreceive(DEFAULT_DATEHJRRECEIVE)
            .heurearch(DEFAULT_HEUREARCH)
            .actiontype(DEFAULT_ACTIONTYPE)
            .comments(DEFAULT_COMMENTS)
            .datearch(DEFAULT_DATEARCH)
            .datearchhj(DEFAULT_DATEARCHHJ)
            .lasttransserial(DEFAULT_LASTTRANSSERIAL)
            .adrsbooktransto(DEFAULT_ADRSBOOKTRANSTO)
            .statusreceiveto(DEFAULT_STATUSRECEIVETO)
            .commentsreceiveto(DEFAULT_COMMENTSRECEIVETO)
            .receivedatejcuserto(DEFAULT_RECEIVEDATEJCUSERTO)
            .receivedatehjruserto(DEFAULT_RECEIVEDATEHJRUSERTO)
            .typetransfer(DEFAULT_TYPETRANSFER)
            .transrecserial(DEFAULT_TRANSRECSERIAL)
            .attach(DEFAULT_ATTACH)
            .transtype(DEFAULT_TRANSTYPE)
            .ordernbr(DEFAULT_ORDERNBR)
            .heureaction(DEFAULT_HEUREACTION)
            .datejcaction(DEFAULT_DATEJCACTION)
            .datehjraction(DEFAULT_DATEHJRACTION)
            .statusdenied(DEFAULT_STATUSDENIED)
            .subjectcorresp(DEFAULT_SUBJECTCORRESP)
            .datejccorresp(DEFAULT_DATEJCCORRESP)
            .datehjrcorresp(DEFAULT_DATEHJRCORRESP)
            .oldstatus(DEFAULT_OLDSTATUS)
            .step(DEFAULT_STEP)
            .typeprocess(DEFAULT_TYPEPROCESS)
            .codetask(DEFAULT_CODETASK)
            .refusetext(DEFAULT_REFUSETEXT)
            .statusrefused(DEFAULT_STATUSREFUSED)
            .bidadrsbook(DEFAULT_BIDADRSBOOK)
            .pagenbrpaper(DEFAULT_PAGENBRPAPER)
            .flagprint(DEFAULT_FLAGPRINT)
            .dateprint(DEFAULT_DATEPRINT)
            .datehjrprint(DEFAULT_DATEHJRPRINT)
            .datejcdelete(DEFAULT_DATEJCDELETE)
            .datejcrevoke(DEFAULT_DATEJCREVOKE)
            .datehjrrevoke(DEFAULT_DATEHJRREVOKE)
            .gabaritcontext(DEFAULT_GABARITCONTEXT)
            .approvedspeech(DEFAULT_APPROVEDSPEECH)
            .datejcapprovedspeech(DEFAULT_DATEJCAPPROVEDSPEECH)
            .datehjrapprovedspeech(DEFAULT_DATEHJRAPPROVEDSPEECH)
            .conformitytask(DEFAULT_CONFORMITYTASK)
            .useradrsbook(DEFAULT_USERADRSBOOK)
            .stepmaxwf(DEFAULT_STEPMAXWF)
            .incidenttransfer(DEFAULT_INCIDENTTRANSFER)
            .qualificationincident(DEFAULT_QUALIFICATIONINCIDENT)
            .categorieincident(DEFAULT_CATEGORIEINCIDENT)
            .statutincident(DEFAULT_STATUTINCIDENT)
            .criticiteincident(DEFAULT_CRITICITEINCIDENT)
            .voiceId(DEFAULT_VOICE_ID)
            .favoris(DEFAULT_FAVORIS)
            .checkinboxfavorite(DEFAULT_CHECKINBOXFAVORITE)
            .checkclosefavorite(DEFAULT_CHECKCLOSEFAVORITE)
            .checkfavorite(DEFAULT_CHECKFAVORITE)
            .taskcategId(DEFAULT_TASKCATEG_ID)
            .pinrequired(DEFAULT_PINREQUIRED)
            .codePin(DEFAULT_CODE_PIN)
            .sendwithmail(DEFAULT_SENDWITHMAIL);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Transfer createUpdatedEntity() {
        return new Transfer()
            .docyear(UPDATED_DOCYEAR)
            .texttransfer(UPDATED_TEXTTRANSFER)
            .datejctransfer(UPDATED_DATEJCTRANSFER)
            .datehjrtransfer(UPDATED_DATEHJRTRANSFER)
            .statustransfer(UPDATED_STATUSTRANSFER)
            .datesendjctransfer(UPDATED_DATESENDJCTRANSFER)
            .datesendhjrtransfer(UPDATED_DATESENDHJRTRANSFER)
            .savetransfer(UPDATED_SAVETRANSFER)
            .numcopy(UPDATED_NUMCOPY)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .timeaction(UPDATED_TIMEACTION)
            .deadline(UPDATED_DEADLINE)
            .rappelnum(UPDATED_RAPPELNUM)
            .rappeltype(UPDATED_RAPPELTYPE)
            .readrequest(UPDATED_READREQUEST)
            .typereceive(UPDATED_TYPERECEIVE)
            .datejcreceive(UPDATED_DATEJCRECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .heurearch(UPDATED_HEUREARCH)
            .actiontype(UPDATED_ACTIONTYPE)
            .comments(UPDATED_COMMENTS)
            .datearch(UPDATED_DATEARCH)
            .datearchhj(UPDATED_DATEARCHHJ)
            .lasttransserial(UPDATED_LASTTRANSSERIAL)
            .adrsbooktransto(UPDATED_ADRSBOOKTRANSTO)
            .statusreceiveto(UPDATED_STATUSRECEIVETO)
            .commentsreceiveto(UPDATED_COMMENTSRECEIVETO)
            .receivedatejcuserto(UPDATED_RECEIVEDATEJCUSERTO)
            .receivedatehjruserto(UPDATED_RECEIVEDATEHJRUSERTO)
            .typetransfer(UPDATED_TYPETRANSFER)
            .transrecserial(UPDATED_TRANSRECSERIAL)
            .attach(UPDATED_ATTACH)
            .transtype(UPDATED_TRANSTYPE)
            .ordernbr(UPDATED_ORDERNBR)
            .heureaction(UPDATED_HEUREACTION)
            .datejcaction(UPDATED_DATEJCACTION)
            .datehjraction(UPDATED_DATEHJRACTION)
            .statusdenied(UPDATED_STATUSDENIED)
            .subjectcorresp(UPDATED_SUBJECTCORRESP)
            .datejccorresp(UPDATED_DATEJCCORRESP)
            .datehjrcorresp(UPDATED_DATEHJRCORRESP)
            .oldstatus(UPDATED_OLDSTATUS)
            .step(UPDATED_STEP)
            .typeprocess(UPDATED_TYPEPROCESS)
            .codetask(UPDATED_CODETASK)
            .refusetext(UPDATED_REFUSETEXT)
            .statusrefused(UPDATED_STATUSREFUSED)
            .bidadrsbook(UPDATED_BIDADRSBOOK)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .flagprint(UPDATED_FLAGPRINT)
            .dateprint(UPDATED_DATEPRINT)
            .datehjrprint(UPDATED_DATEHJRPRINT)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .gabaritcontext(UPDATED_GABARITCONTEXT)
            .approvedspeech(UPDATED_APPROVEDSPEECH)
            .datejcapprovedspeech(UPDATED_DATEJCAPPROVEDSPEECH)
            .datehjrapprovedspeech(UPDATED_DATEHJRAPPROVEDSPEECH)
            .conformitytask(UPDATED_CONFORMITYTASK)
            .useradrsbook(UPDATED_USERADRSBOOK)
            .stepmaxwf(UPDATED_STEPMAXWF)
            .incidenttransfer(UPDATED_INCIDENTTRANSFER)
            .qualificationincident(UPDATED_QUALIFICATIONINCIDENT)
            .categorieincident(UPDATED_CATEGORIEINCIDENT)
            .statutincident(UPDATED_STATUTINCIDENT)
            .criticiteincident(UPDATED_CRITICITEINCIDENT)
            .voiceId(UPDATED_VOICE_ID)
            .favoris(UPDATED_FAVORIS)
            .checkinboxfavorite(UPDATED_CHECKINBOXFAVORITE)
            .checkclosefavorite(UPDATED_CHECKCLOSEFAVORITE)
            .checkfavorite(UPDATED_CHECKFAVORITE)
            .taskcategId(UPDATED_TASKCATEG_ID)
            .pinrequired(UPDATED_PINREQUIRED)
            .codePin(UPDATED_CODE_PIN)
            .sendwithmail(UPDATED_SENDWITHMAIL);
    }

    @BeforeEach
    void initTest() {
        transfer = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTransfer != null) {
            transferRepository.delete(insertedTransfer);
            insertedTransfer = null;
        }
    }

    @Test
    @Transactional
    void createTransfer() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);
        var returnedTransferDTO = om.readValue(
            restTransferMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(transferDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TransferDTO.class
        );

        // Validate the Transfer in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTransfer = transferMapper.toEntity(returnedTransferDTO);
        assertTransferUpdatableFieldsEquals(returnedTransfer, getPersistedTransfer(returnedTransfer));

        insertedTransfer = returnedTransfer;
    }

    @Test
    @Transactional
    void createTransferWithExistingId() throws Exception {
        // Create the Transfer with an existing ID
        transfer.setId(1L);
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTransferMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(transferDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllTransfers() throws Exception {
        // Initialize the database
        insertedTransfer = transferRepository.saveAndFlush(transfer);

        // Get all the transferList
        restTransferMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(transfer.getId().intValue())))
            .andExpect(jsonPath("$.[*].docyear").value(hasItem(DEFAULT_DOCYEAR)))
            .andExpect(jsonPath("$.[*].texttransfer").value(hasItem(DEFAULT_TEXTTRANSFER)))
            .andExpect(jsonPath("$.[*].datejctransfer").value(hasItem(DEFAULT_DATEJCTRANSFER.toString())))
            .andExpect(jsonPath("$.[*].datehjrtransfer").value(hasItem(DEFAULT_DATEHJRTRANSFER)))
            .andExpect(jsonPath("$.[*].statustransfer").value(hasItem(DEFAULT_STATUSTRANSFER)))
            .andExpect(jsonPath("$.[*].datesendjctransfer").value(hasItem(DEFAULT_DATESENDJCTRANSFER.toString())))
            .andExpect(jsonPath("$.[*].datesendhjrtransfer").value(hasItem(DEFAULT_DATESENDHJRTRANSFER)))
            .andExpect(jsonPath("$.[*].savetransfer").value(hasItem(DEFAULT_SAVETRANSFER)))
            .andExpect(jsonPath("$.[*].numcopy").value(hasItem(DEFAULT_NUMCOPY)))
            .andExpect(jsonPath("$.[*].highlevel").value(hasItem(DEFAULT_HIGHLEVEL)))
            .andExpect(jsonPath("$.[*].confidentiel").value(hasItem(DEFAULT_CONFIDENTIEL)))
            .andExpect(jsonPath("$.[*].priority").value(hasItem(DEFAULT_PRIORITY)))
            .andExpect(jsonPath("$.[*].timeaction").value(hasItem(DEFAULT_TIMEACTION)))
            .andExpect(jsonPath("$.[*].deadline").value(hasItem(DEFAULT_DEADLINE.toString())))
            .andExpect(jsonPath("$.[*].rappelnum").value(hasItem(DEFAULT_RAPPELNUM)))
            .andExpect(jsonPath("$.[*].rappeltype").value(hasItem(DEFAULT_RAPPELTYPE)))
            .andExpect(jsonPath("$.[*].readrequest").value(hasItem(DEFAULT_READREQUEST)))
            .andExpect(jsonPath("$.[*].typereceive").value(hasItem(DEFAULT_TYPERECEIVE)))
            .andExpect(jsonPath("$.[*].datejcreceive").value(hasItem(DEFAULT_DATEJCRECEIVE.toString())))
            .andExpect(jsonPath("$.[*].datehjrreceive").value(hasItem(DEFAULT_DATEHJRRECEIVE)))
            .andExpect(jsonPath("$.[*].heurearch").value(hasItem(DEFAULT_HEUREARCH)))
            .andExpect(jsonPath("$.[*].actiontype").value(hasItem(DEFAULT_ACTIONTYPE)))
            .andExpect(jsonPath("$.[*].comments").value(hasItem(DEFAULT_COMMENTS)))
            .andExpect(jsonPath("$.[*].datearch").value(hasItem(DEFAULT_DATEARCH.toString())))
            .andExpect(jsonPath("$.[*].datearchhj").value(hasItem(DEFAULT_DATEARCHHJ)))
            .andExpect(jsonPath("$.[*].lasttransserial").value(hasItem(DEFAULT_LASTTRANSSERIAL)))
            .andExpect(jsonPath("$.[*].adrsbooktransto").value(hasItem(DEFAULT_ADRSBOOKTRANSTO)))
            .andExpect(jsonPath("$.[*].statusreceiveto").value(hasItem(DEFAULT_STATUSRECEIVETO)))
            .andExpect(jsonPath("$.[*].commentsreceiveto").value(hasItem(DEFAULT_COMMENTSRECEIVETO)))
            .andExpect(jsonPath("$.[*].receivedatejcuserto").value(hasItem(DEFAULT_RECEIVEDATEJCUSERTO.toString())))
            .andExpect(jsonPath("$.[*].receivedatehjruserto").value(hasItem(DEFAULT_RECEIVEDATEHJRUSERTO)))
            .andExpect(jsonPath("$.[*].typetransfer").value(hasItem(DEFAULT_TYPETRANSFER)))
            .andExpect(jsonPath("$.[*].transrecserial").value(hasItem(DEFAULT_TRANSRECSERIAL)))
            .andExpect(jsonPath("$.[*].attach").value(hasItem(DEFAULT_ATTACH)))
            .andExpect(jsonPath("$.[*].transtype").value(hasItem(DEFAULT_TRANSTYPE)))
            .andExpect(jsonPath("$.[*].ordernbr").value(hasItem(DEFAULT_ORDERNBR)))
            .andExpect(jsonPath("$.[*].heureaction").value(hasItem(DEFAULT_HEUREACTION)))
            .andExpect(jsonPath("$.[*].datejcaction").value(hasItem(DEFAULT_DATEJCACTION.toString())))
            .andExpect(jsonPath("$.[*].datehjraction").value(hasItem(DEFAULT_DATEHJRACTION)))
            .andExpect(jsonPath("$.[*].statusdenied").value(hasItem(DEFAULT_STATUSDENIED)))
            .andExpect(jsonPath("$.[*].subjectcorresp").value(hasItem(DEFAULT_SUBJECTCORRESP)))
            .andExpect(jsonPath("$.[*].datejccorresp").value(hasItem(DEFAULT_DATEJCCORRESP.toString())))
            .andExpect(jsonPath("$.[*].datehjrcorresp").value(hasItem(DEFAULT_DATEHJRCORRESP)))
            .andExpect(jsonPath("$.[*].oldstatus").value(hasItem(DEFAULT_OLDSTATUS)))
            .andExpect(jsonPath("$.[*].step").value(hasItem(DEFAULT_STEP)))
            .andExpect(jsonPath("$.[*].typeprocess").value(hasItem(DEFAULT_TYPEPROCESS)))
            .andExpect(jsonPath("$.[*].codetask").value(hasItem(DEFAULT_CODETASK)))
            .andExpect(jsonPath("$.[*].refusetext").value(hasItem(DEFAULT_REFUSETEXT)))
            .andExpect(jsonPath("$.[*].statusrefused").value(hasItem(DEFAULT_STATUSREFUSED)))
            .andExpect(jsonPath("$.[*].bidadrsbook").value(hasItem(DEFAULT_BIDADRSBOOK)))
            .andExpect(jsonPath("$.[*].pagenbrpaper").value(hasItem(DEFAULT_PAGENBRPAPER)))
            .andExpect(jsonPath("$.[*].flagprint").value(hasItem(DEFAULT_FLAGPRINT)))
            .andExpect(jsonPath("$.[*].dateprint").value(hasItem(DEFAULT_DATEPRINT.toString())))
            .andExpect(jsonPath("$.[*].datehjrprint").value(hasItem(DEFAULT_DATEHJRPRINT)))
            .andExpect(jsonPath("$.[*].datejcdelete").value(hasItem(DEFAULT_DATEJCDELETE.toString())))
            .andExpect(jsonPath("$.[*].datejcrevoke").value(hasItem(DEFAULT_DATEJCREVOKE.toString())))
            .andExpect(jsonPath("$.[*].datehjrrevoke").value(hasItem(DEFAULT_DATEHJRREVOKE)))
            .andExpect(jsonPath("$.[*].gabaritcontext").value(hasItem(DEFAULT_GABARITCONTEXT)))
            .andExpect(jsonPath("$.[*].approvedspeech").value(hasItem(DEFAULT_APPROVEDSPEECH)))
            .andExpect(jsonPath("$.[*].datejcapprovedspeech").value(hasItem(DEFAULT_DATEJCAPPROVEDSPEECH.toString())))
            .andExpect(jsonPath("$.[*].datehjrapprovedspeech").value(hasItem(DEFAULT_DATEHJRAPPROVEDSPEECH)))
            .andExpect(jsonPath("$.[*].conformitytask").value(hasItem(DEFAULT_CONFORMITYTASK)))
            .andExpect(jsonPath("$.[*].useradrsbook").value(hasItem(DEFAULT_USERADRSBOOK)))
            .andExpect(jsonPath("$.[*].stepmaxwf").value(hasItem(DEFAULT_STEPMAXWF)))
            .andExpect(jsonPath("$.[*].incidenttransfer").value(hasItem(DEFAULT_INCIDENTTRANSFER)))
            .andExpect(jsonPath("$.[*].qualificationincident").value(hasItem(DEFAULT_QUALIFICATIONINCIDENT)))
            .andExpect(jsonPath("$.[*].categorieincident").value(hasItem(DEFAULT_CATEGORIEINCIDENT)))
            .andExpect(jsonPath("$.[*].statutincident").value(hasItem(DEFAULT_STATUTINCIDENT)))
            .andExpect(jsonPath("$.[*].criticiteincident").value(hasItem(DEFAULT_CRITICITEINCIDENT)))
            .andExpect(jsonPath("$.[*].voiceId").value(hasItem(DEFAULT_VOICE_ID)))
            .andExpect(jsonPath("$.[*].favoris").value(hasItem(DEFAULT_FAVORIS)))
            .andExpect(jsonPath("$.[*].checkinboxfavorite").value(hasItem(DEFAULT_CHECKINBOXFAVORITE)))
            .andExpect(jsonPath("$.[*].checkclosefavorite").value(hasItem(DEFAULT_CHECKCLOSEFAVORITE)))
            .andExpect(jsonPath("$.[*].checkfavorite").value(hasItem(DEFAULT_CHECKFAVORITE)))
            .andExpect(jsonPath("$.[*].taskcategId").value(hasItem(DEFAULT_TASKCATEG_ID)))
            .andExpect(jsonPath("$.[*].pinrequired").value(hasItem(DEFAULT_PINREQUIRED)))
            .andExpect(jsonPath("$.[*].codePin").value(hasItem(DEFAULT_CODE_PIN)))
            .andExpect(jsonPath("$.[*].sendwithmail").value(hasItem(DEFAULT_SENDWITHMAIL)));
    }

    @Test
    @Transactional
    void getTransfer() throws Exception {
        // Initialize the database
        insertedTransfer = transferRepository.saveAndFlush(transfer);

        // Get the transfer
        restTransferMockMvc
            .perform(get(ENTITY_API_URL_ID, transfer.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(transfer.getId().intValue()))
            .andExpect(jsonPath("$.docyear").value(DEFAULT_DOCYEAR))
            .andExpect(jsonPath("$.texttransfer").value(DEFAULT_TEXTTRANSFER))
            .andExpect(jsonPath("$.datejctransfer").value(DEFAULT_DATEJCTRANSFER.toString()))
            .andExpect(jsonPath("$.datehjrtransfer").value(DEFAULT_DATEHJRTRANSFER))
            .andExpect(jsonPath("$.statustransfer").value(DEFAULT_STATUSTRANSFER))
            .andExpect(jsonPath("$.datesendjctransfer").value(DEFAULT_DATESENDJCTRANSFER.toString()))
            .andExpect(jsonPath("$.datesendhjrtransfer").value(DEFAULT_DATESENDHJRTRANSFER))
            .andExpect(jsonPath("$.savetransfer").value(DEFAULT_SAVETRANSFER))
            .andExpect(jsonPath("$.numcopy").value(DEFAULT_NUMCOPY))
            .andExpect(jsonPath("$.highlevel").value(DEFAULT_HIGHLEVEL))
            .andExpect(jsonPath("$.confidentiel").value(DEFAULT_CONFIDENTIEL))
            .andExpect(jsonPath("$.priority").value(DEFAULT_PRIORITY))
            .andExpect(jsonPath("$.timeaction").value(DEFAULT_TIMEACTION))
            .andExpect(jsonPath("$.deadline").value(DEFAULT_DEADLINE.toString()))
            .andExpect(jsonPath("$.rappelnum").value(DEFAULT_RAPPELNUM))
            .andExpect(jsonPath("$.rappeltype").value(DEFAULT_RAPPELTYPE))
            .andExpect(jsonPath("$.readrequest").value(DEFAULT_READREQUEST))
            .andExpect(jsonPath("$.typereceive").value(DEFAULT_TYPERECEIVE))
            .andExpect(jsonPath("$.datejcreceive").value(DEFAULT_DATEJCRECEIVE.toString()))
            .andExpect(jsonPath("$.datehjrreceive").value(DEFAULT_DATEHJRRECEIVE))
            .andExpect(jsonPath("$.heurearch").value(DEFAULT_HEUREARCH))
            .andExpect(jsonPath("$.actiontype").value(DEFAULT_ACTIONTYPE))
            .andExpect(jsonPath("$.comments").value(DEFAULT_COMMENTS))
            .andExpect(jsonPath("$.datearch").value(DEFAULT_DATEARCH.toString()))
            .andExpect(jsonPath("$.datearchhj").value(DEFAULT_DATEARCHHJ))
            .andExpect(jsonPath("$.lasttransserial").value(DEFAULT_LASTTRANSSERIAL))
            .andExpect(jsonPath("$.adrsbooktransto").value(DEFAULT_ADRSBOOKTRANSTO))
            .andExpect(jsonPath("$.statusreceiveto").value(DEFAULT_STATUSRECEIVETO))
            .andExpect(jsonPath("$.commentsreceiveto").value(DEFAULT_COMMENTSRECEIVETO))
            .andExpect(jsonPath("$.receivedatejcuserto").value(DEFAULT_RECEIVEDATEJCUSERTO.toString()))
            .andExpect(jsonPath("$.receivedatehjruserto").value(DEFAULT_RECEIVEDATEHJRUSERTO))
            .andExpect(jsonPath("$.typetransfer").value(DEFAULT_TYPETRANSFER))
            .andExpect(jsonPath("$.transrecserial").value(DEFAULT_TRANSRECSERIAL))
            .andExpect(jsonPath("$.attach").value(DEFAULT_ATTACH))
            .andExpect(jsonPath("$.transtype").value(DEFAULT_TRANSTYPE))
            .andExpect(jsonPath("$.ordernbr").value(DEFAULT_ORDERNBR))
            .andExpect(jsonPath("$.heureaction").value(DEFAULT_HEUREACTION))
            .andExpect(jsonPath("$.datejcaction").value(DEFAULT_DATEJCACTION.toString()))
            .andExpect(jsonPath("$.datehjraction").value(DEFAULT_DATEHJRACTION))
            .andExpect(jsonPath("$.statusdenied").value(DEFAULT_STATUSDENIED))
            .andExpect(jsonPath("$.subjectcorresp").value(DEFAULT_SUBJECTCORRESP))
            .andExpect(jsonPath("$.datejccorresp").value(DEFAULT_DATEJCCORRESP.toString()))
            .andExpect(jsonPath("$.datehjrcorresp").value(DEFAULT_DATEHJRCORRESP))
            .andExpect(jsonPath("$.oldstatus").value(DEFAULT_OLDSTATUS))
            .andExpect(jsonPath("$.step").value(DEFAULT_STEP))
            .andExpect(jsonPath("$.typeprocess").value(DEFAULT_TYPEPROCESS))
            .andExpect(jsonPath("$.codetask").value(DEFAULT_CODETASK))
            .andExpect(jsonPath("$.refusetext").value(DEFAULT_REFUSETEXT))
            .andExpect(jsonPath("$.statusrefused").value(DEFAULT_STATUSREFUSED))
            .andExpect(jsonPath("$.bidadrsbook").value(DEFAULT_BIDADRSBOOK))
            .andExpect(jsonPath("$.pagenbrpaper").value(DEFAULT_PAGENBRPAPER))
            .andExpect(jsonPath("$.flagprint").value(DEFAULT_FLAGPRINT))
            .andExpect(jsonPath("$.dateprint").value(DEFAULT_DATEPRINT.toString()))
            .andExpect(jsonPath("$.datehjrprint").value(DEFAULT_DATEHJRPRINT))
            .andExpect(jsonPath("$.datejcdelete").value(DEFAULT_DATEJCDELETE.toString()))
            .andExpect(jsonPath("$.datejcrevoke").value(DEFAULT_DATEJCREVOKE.toString()))
            .andExpect(jsonPath("$.datehjrrevoke").value(DEFAULT_DATEHJRREVOKE))
            .andExpect(jsonPath("$.gabaritcontext").value(DEFAULT_GABARITCONTEXT))
            .andExpect(jsonPath("$.approvedspeech").value(DEFAULT_APPROVEDSPEECH))
            .andExpect(jsonPath("$.datejcapprovedspeech").value(DEFAULT_DATEJCAPPROVEDSPEECH.toString()))
            .andExpect(jsonPath("$.datehjrapprovedspeech").value(DEFAULT_DATEHJRAPPROVEDSPEECH))
            .andExpect(jsonPath("$.conformitytask").value(DEFAULT_CONFORMITYTASK))
            .andExpect(jsonPath("$.useradrsbook").value(DEFAULT_USERADRSBOOK))
            .andExpect(jsonPath("$.stepmaxwf").value(DEFAULT_STEPMAXWF))
            .andExpect(jsonPath("$.incidenttransfer").value(DEFAULT_INCIDENTTRANSFER))
            .andExpect(jsonPath("$.qualificationincident").value(DEFAULT_QUALIFICATIONINCIDENT))
            .andExpect(jsonPath("$.categorieincident").value(DEFAULT_CATEGORIEINCIDENT))
            .andExpect(jsonPath("$.statutincident").value(DEFAULT_STATUTINCIDENT))
            .andExpect(jsonPath("$.criticiteincident").value(DEFAULT_CRITICITEINCIDENT))
            .andExpect(jsonPath("$.voiceId").value(DEFAULT_VOICE_ID))
            .andExpect(jsonPath("$.favoris").value(DEFAULT_FAVORIS))
            .andExpect(jsonPath("$.checkinboxfavorite").value(DEFAULT_CHECKINBOXFAVORITE))
            .andExpect(jsonPath("$.checkclosefavorite").value(DEFAULT_CHECKCLOSEFAVORITE))
            .andExpect(jsonPath("$.checkfavorite").value(DEFAULT_CHECKFAVORITE))
            .andExpect(jsonPath("$.taskcategId").value(DEFAULT_TASKCATEG_ID))
            .andExpect(jsonPath("$.pinrequired").value(DEFAULT_PINREQUIRED))
            .andExpect(jsonPath("$.codePin").value(DEFAULT_CODE_PIN))
            .andExpect(jsonPath("$.sendwithmail").value(DEFAULT_SENDWITHMAIL));
    }

    @Test
    @Transactional
    void getNonExistingTransfer() throws Exception {
        // Get the transfer
        restTransferMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTransfer() throws Exception {
        // Initialize the database
        insertedTransfer = transferRepository.saveAndFlush(transfer);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the transfer
        Transfer updatedTransfer = transferRepository.findById(transfer.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTransfer are not directly saved in db
        em.detach(updatedTransfer);
        updatedTransfer
            .docyear(UPDATED_DOCYEAR)
            .texttransfer(UPDATED_TEXTTRANSFER)
            .datejctransfer(UPDATED_DATEJCTRANSFER)
            .datehjrtransfer(UPDATED_DATEHJRTRANSFER)
            .statustransfer(UPDATED_STATUSTRANSFER)
            .datesendjctransfer(UPDATED_DATESENDJCTRANSFER)
            .datesendhjrtransfer(UPDATED_DATESENDHJRTRANSFER)
            .savetransfer(UPDATED_SAVETRANSFER)
            .numcopy(UPDATED_NUMCOPY)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .timeaction(UPDATED_TIMEACTION)
            .deadline(UPDATED_DEADLINE)
            .rappelnum(UPDATED_RAPPELNUM)
            .rappeltype(UPDATED_RAPPELTYPE)
            .readrequest(UPDATED_READREQUEST)
            .typereceive(UPDATED_TYPERECEIVE)
            .datejcreceive(UPDATED_DATEJCRECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .heurearch(UPDATED_HEUREARCH)
            .actiontype(UPDATED_ACTIONTYPE)
            .comments(UPDATED_COMMENTS)
            .datearch(UPDATED_DATEARCH)
            .datearchhj(UPDATED_DATEARCHHJ)
            .lasttransserial(UPDATED_LASTTRANSSERIAL)
            .adrsbooktransto(UPDATED_ADRSBOOKTRANSTO)
            .statusreceiveto(UPDATED_STATUSRECEIVETO)
            .commentsreceiveto(UPDATED_COMMENTSRECEIVETO)
            .receivedatejcuserto(UPDATED_RECEIVEDATEJCUSERTO)
            .receivedatehjruserto(UPDATED_RECEIVEDATEHJRUSERTO)
            .typetransfer(UPDATED_TYPETRANSFER)
            .transrecserial(UPDATED_TRANSRECSERIAL)
            .attach(UPDATED_ATTACH)
            .transtype(UPDATED_TRANSTYPE)
            .ordernbr(UPDATED_ORDERNBR)
            .heureaction(UPDATED_HEUREACTION)
            .datejcaction(UPDATED_DATEJCACTION)
            .datehjraction(UPDATED_DATEHJRACTION)
            .statusdenied(UPDATED_STATUSDENIED)
            .subjectcorresp(UPDATED_SUBJECTCORRESP)
            .datejccorresp(UPDATED_DATEJCCORRESP)
            .datehjrcorresp(UPDATED_DATEHJRCORRESP)
            .oldstatus(UPDATED_OLDSTATUS)
            .step(UPDATED_STEP)
            .typeprocess(UPDATED_TYPEPROCESS)
            .codetask(UPDATED_CODETASK)
            .refusetext(UPDATED_REFUSETEXT)
            .statusrefused(UPDATED_STATUSREFUSED)
            .bidadrsbook(UPDATED_BIDADRSBOOK)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .flagprint(UPDATED_FLAGPRINT)
            .dateprint(UPDATED_DATEPRINT)
            .datehjrprint(UPDATED_DATEHJRPRINT)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .gabaritcontext(UPDATED_GABARITCONTEXT)
            .approvedspeech(UPDATED_APPROVEDSPEECH)
            .datejcapprovedspeech(UPDATED_DATEJCAPPROVEDSPEECH)
            .datehjrapprovedspeech(UPDATED_DATEHJRAPPROVEDSPEECH)
            .conformitytask(UPDATED_CONFORMITYTASK)
            .useradrsbook(UPDATED_USERADRSBOOK)
            .stepmaxwf(UPDATED_STEPMAXWF)
            .incidenttransfer(UPDATED_INCIDENTTRANSFER)
            .qualificationincident(UPDATED_QUALIFICATIONINCIDENT)
            .categorieincident(UPDATED_CATEGORIEINCIDENT)
            .statutincident(UPDATED_STATUTINCIDENT)
            .criticiteincident(UPDATED_CRITICITEINCIDENT)
            .voiceId(UPDATED_VOICE_ID)
            .favoris(UPDATED_FAVORIS)
            .checkinboxfavorite(UPDATED_CHECKINBOXFAVORITE)
            .checkclosefavorite(UPDATED_CHECKCLOSEFAVORITE)
            .checkfavorite(UPDATED_CHECKFAVORITE)
            .taskcategId(UPDATED_TASKCATEG_ID)
            .pinrequired(UPDATED_PINREQUIRED)
            .codePin(UPDATED_CODE_PIN)
            .sendwithmail(UPDATED_SENDWITHMAIL);
        TransferDTO transferDTO = transferMapper.toDto(updatedTransfer);

        restTransferMockMvc
            .perform(
                put(ENTITY_API_URL_ID, transferDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(transferDTO))
            )
            .andExpect(status().isOk());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTransferToMatchAllProperties(updatedTransfer);
    }

    @Test
    @Transactional
    void putNonExistingTransfer() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        transfer.setId(longCount.incrementAndGet());

        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTransferMockMvc
            .perform(
                put(ENTITY_API_URL_ID, transferDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(transferDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTransfer() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        transfer.setId(longCount.incrementAndGet());

        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTransferMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(transferDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTransfer() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        transfer.setId(longCount.incrementAndGet());

        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTransferMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(transferDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTransferWithPatch() throws Exception {
        // Initialize the database
        insertedTransfer = transferRepository.saveAndFlush(transfer);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the transfer using partial update
        Transfer partialUpdatedTransfer = new Transfer();
        partialUpdatedTransfer.setId(transfer.getId());

        partialUpdatedTransfer
            .texttransfer(UPDATED_TEXTTRANSFER)
            .datesendjctransfer(UPDATED_DATESENDJCTRANSFER)
            .datesendhjrtransfer(UPDATED_DATESENDHJRTRANSFER)
            .savetransfer(UPDATED_SAVETRANSFER)
            .numcopy(UPDATED_NUMCOPY)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .timeaction(UPDATED_TIMEACTION)
            .deadline(UPDATED_DEADLINE)
            .rappeltype(UPDATED_RAPPELTYPE)
            .readrequest(UPDATED_READREQUEST)
            .typereceive(UPDATED_TYPERECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .datearch(UPDATED_DATEARCH)
            .statusreceiveto(UPDATED_STATUSRECEIVETO)
            .commentsreceiveto(UPDATED_COMMENTSRECEIVETO)
            .receivedatehjruserto(UPDATED_RECEIVEDATEHJRUSERTO)
            .transrecserial(UPDATED_TRANSRECSERIAL)
            .transtype(UPDATED_TRANSTYPE)
            .subjectcorresp(UPDATED_SUBJECTCORRESP)
            .datejccorresp(UPDATED_DATEJCCORRESP)
            .step(UPDATED_STEP)
            .codetask(UPDATED_CODETASK)
            .statusrefused(UPDATED_STATUSREFUSED)
            .bidadrsbook(UPDATED_BIDADRSBOOK)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .flagprint(UPDATED_FLAGPRINT)
            .datehjrprint(UPDATED_DATEHJRPRINT)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .gabaritcontext(UPDATED_GABARITCONTEXT)
            .approvedspeech(UPDATED_APPROVEDSPEECH)
            .datejcapprovedspeech(UPDATED_DATEJCAPPROVEDSPEECH)
            .conformitytask(UPDATED_CONFORMITYTASK)
            .stepmaxwf(UPDATED_STEPMAXWF)
            .categorieincident(UPDATED_CATEGORIEINCIDENT)
            .criticiteincident(UPDATED_CRITICITEINCIDENT)
            .checkinboxfavorite(UPDATED_CHECKINBOXFAVORITE)
            .checkclosefavorite(UPDATED_CHECKCLOSEFAVORITE)
            .checkfavorite(UPDATED_CHECKFAVORITE)
            .taskcategId(UPDATED_TASKCATEG_ID)
            .sendwithmail(UPDATED_SENDWITHMAIL);

        restTransferMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTransfer.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTransfer))
            )
            .andExpect(status().isOk());

        // Validate the Transfer in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTransferUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedTransfer, transfer), getPersistedTransfer(transfer));
    }

    @Test
    @Transactional
    void fullUpdateTransferWithPatch() throws Exception {
        // Initialize the database
        insertedTransfer = transferRepository.saveAndFlush(transfer);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the transfer using partial update
        Transfer partialUpdatedTransfer = new Transfer();
        partialUpdatedTransfer.setId(transfer.getId());

        partialUpdatedTransfer
            .docyear(UPDATED_DOCYEAR)
            .texttransfer(UPDATED_TEXTTRANSFER)
            .datejctransfer(UPDATED_DATEJCTRANSFER)
            .datehjrtransfer(UPDATED_DATEHJRTRANSFER)
            .statustransfer(UPDATED_STATUSTRANSFER)
            .datesendjctransfer(UPDATED_DATESENDJCTRANSFER)
            .datesendhjrtransfer(UPDATED_DATESENDHJRTRANSFER)
            .savetransfer(UPDATED_SAVETRANSFER)
            .numcopy(UPDATED_NUMCOPY)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .timeaction(UPDATED_TIMEACTION)
            .deadline(UPDATED_DEADLINE)
            .rappelnum(UPDATED_RAPPELNUM)
            .rappeltype(UPDATED_RAPPELTYPE)
            .readrequest(UPDATED_READREQUEST)
            .typereceive(UPDATED_TYPERECEIVE)
            .datejcreceive(UPDATED_DATEJCRECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .heurearch(UPDATED_HEUREARCH)
            .actiontype(UPDATED_ACTIONTYPE)
            .comments(UPDATED_COMMENTS)
            .datearch(UPDATED_DATEARCH)
            .datearchhj(UPDATED_DATEARCHHJ)
            .lasttransserial(UPDATED_LASTTRANSSERIAL)
            .adrsbooktransto(UPDATED_ADRSBOOKTRANSTO)
            .statusreceiveto(UPDATED_STATUSRECEIVETO)
            .commentsreceiveto(UPDATED_COMMENTSRECEIVETO)
            .receivedatejcuserto(UPDATED_RECEIVEDATEJCUSERTO)
            .receivedatehjruserto(UPDATED_RECEIVEDATEHJRUSERTO)
            .typetransfer(UPDATED_TYPETRANSFER)
            .transrecserial(UPDATED_TRANSRECSERIAL)
            .attach(UPDATED_ATTACH)
            .transtype(UPDATED_TRANSTYPE)
            .ordernbr(UPDATED_ORDERNBR)
            .heureaction(UPDATED_HEUREACTION)
            .datejcaction(UPDATED_DATEJCACTION)
            .datehjraction(UPDATED_DATEHJRACTION)
            .statusdenied(UPDATED_STATUSDENIED)
            .subjectcorresp(UPDATED_SUBJECTCORRESP)
            .datejccorresp(UPDATED_DATEJCCORRESP)
            .datehjrcorresp(UPDATED_DATEHJRCORRESP)
            .oldstatus(UPDATED_OLDSTATUS)
            .step(UPDATED_STEP)
            .typeprocess(UPDATED_TYPEPROCESS)
            .codetask(UPDATED_CODETASK)
            .refusetext(UPDATED_REFUSETEXT)
            .statusrefused(UPDATED_STATUSREFUSED)
            .bidadrsbook(UPDATED_BIDADRSBOOK)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .flagprint(UPDATED_FLAGPRINT)
            .dateprint(UPDATED_DATEPRINT)
            .datehjrprint(UPDATED_DATEHJRPRINT)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .gabaritcontext(UPDATED_GABARITCONTEXT)
            .approvedspeech(UPDATED_APPROVEDSPEECH)
            .datejcapprovedspeech(UPDATED_DATEJCAPPROVEDSPEECH)
            .datehjrapprovedspeech(UPDATED_DATEHJRAPPROVEDSPEECH)
            .conformitytask(UPDATED_CONFORMITYTASK)
            .useradrsbook(UPDATED_USERADRSBOOK)
            .stepmaxwf(UPDATED_STEPMAXWF)
            .incidenttransfer(UPDATED_INCIDENTTRANSFER)
            .qualificationincident(UPDATED_QUALIFICATIONINCIDENT)
            .categorieincident(UPDATED_CATEGORIEINCIDENT)
            .statutincident(UPDATED_STATUTINCIDENT)
            .criticiteincident(UPDATED_CRITICITEINCIDENT)
            .voiceId(UPDATED_VOICE_ID)
            .favoris(UPDATED_FAVORIS)
            .checkinboxfavorite(UPDATED_CHECKINBOXFAVORITE)
            .checkclosefavorite(UPDATED_CHECKCLOSEFAVORITE)
            .checkfavorite(UPDATED_CHECKFAVORITE)
            .taskcategId(UPDATED_TASKCATEG_ID)
            .pinrequired(UPDATED_PINREQUIRED)
            .codePin(UPDATED_CODE_PIN)
            .sendwithmail(UPDATED_SENDWITHMAIL);

        restTransferMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTransfer.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTransfer))
            )
            .andExpect(status().isOk());

        // Validate the Transfer in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTransferUpdatableFieldsEquals(partialUpdatedTransfer, getPersistedTransfer(partialUpdatedTransfer));
    }

    @Test
    @Transactional
    void patchNonExistingTransfer() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        transfer.setId(longCount.incrementAndGet());

        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTransferMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, transferDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(transferDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTransfer() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        transfer.setId(longCount.incrementAndGet());

        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTransferMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(transferDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTransfer() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        transfer.setId(longCount.incrementAndGet());

        // Create the Transfer
        TransferDTO transferDTO = transferMapper.toDto(transfer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTransferMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(transferDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Transfer in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTransfer() throws Exception {
        // Initialize the database
        insertedTransfer = transferRepository.saveAndFlush(transfer);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the transfer
        restTransferMockMvc
            .perform(delete(ENTITY_API_URL_ID, transfer.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return transferRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Transfer getPersistedTransfer(Transfer transfer) {
        return transferRepository.findById(transfer.getId()).orElseThrow();
    }

    protected void assertPersistedTransferToMatchAllProperties(Transfer expectedTransfer) {
        assertTransferAllPropertiesEquals(expectedTransfer, getPersistedTransfer(expectedTransfer));
    }

    protected void assertPersistedTransferToMatchUpdatableProperties(Transfer expectedTransfer) {
        assertTransferAllUpdatablePropertiesEquals(expectedTransfer, getPersistedTransfer(expectedTransfer));
    }
}
